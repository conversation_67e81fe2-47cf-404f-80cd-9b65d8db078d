<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Quantum Atom Close-Up</title>
<style>
  html, body { margin:0; padding:0; width:100%; height:100%; overflow:hidden; background:#000;}
  canvas { display:block; }
</style>
</head>
<body>
<canvas id="c" width="1920" height="1080"></canvas>
<script>
const canvas = document.getElementById('c');
const ctx = canvas.getContext('2d');

const W = canvas.width;
const H = canvas.height;
const CX = W/2;
const CY = H/2;

let particles = [];
const MAX_PARTICLES = 200;

// Create orbiting particles (electrons)
for(let i=0;i<MAX_PARTICLES;i++){
  const orbitRadius = Math.random()*200 + 100;
  const angle = Math.random()*Math.PI*2;
  const speed = (Math.random()*0.5 + 0.2)*(Math.random()<0.5?1:-1);
  particles.push({angle, orbitRadius, speed, size: Math.random()*6+2});
}

let time = 0;

// Draw nucleus
function drawNucleus(){
  const gradient = ctx.createRadialGradient(CX, CY, 0, CX, CY, 50);
  gradient.addColorStop(0,'rgba(255,255,255,1)');
  gradient.addColorStop(0.4,'rgba(200,200,255,0.7)');
  gradient.addColorStop(1,'rgba(100,50,255,0.1)');
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(CX, CY, 50, 0, Math.PI*2);
  ctx.fill();
}

// Draw orbiting electrons
function drawParticles(){
  particles.forEach(p=>{
    p.angle += p.speed*0.01;
    const x = CX + Math.cos(p.angle+time*0.1)*p.orbitRadius;
    const y = CY + Math.sin(p.angle+time*0.1*0.9)*p.orbitRadius*0.6;
    
    // glow
    const grad = ctx.createRadialGradient(x,y,0,x,y,p.size*3);
    grad.addColorStop(0,'rgba(255,255,255,0.8)');
    grad.addColorStop(0.2,'rgba(150,150,255,0.5)');
    grad.addColorStop(1,'rgba(0,0,0,0)');
    ctx.fillStyle = grad;
    ctx.beginPath();
    ctx.arc(x,y,p.size,0,Math.PI*2);
    ctx.fill();
    
    // orbit lines
    ctx.strokeStyle='rgba(100,100,255,0.05)';
    ctx.lineWidth=1;
    ctx.beginPath();
    ctx.arc(CX,CY,p.orbitRadius,0,Math.PI*2);
    ctx.stroke();
  });
}

// Apply mirror effect
function applyMirror(){
  // save original
  const image = ctx.getImageData(0,0,W,H);
  ctx.globalAlpha = 0.3;
  // horizontal mirror
  ctx.putImageData(image,0,0);
  ctx.save();
  ctx.translate(W,0);
  ctx.scale(-1,1);
  ctx.drawImage(canvas,0,0);
  ctx.restore();
  // vertical mirror
  ctx.save();
  ctx.translate(0,H);
  ctx.scale(1,-1);
  ctx.drawImage(canvas,0,0);
  ctx.restore();
  ctx.globalAlpha =1;
}

function render(){
  time += 1;
  // fade background slightly to create trails
  ctx.fillStyle = 'rgba(0,0,0,0.12)';
  ctx.fillRect(0,0,W,H);
  
  drawNucleus();
  drawParticles();
  applyMirror();
  
  requestAnimationFrame(render);
}

render();
</script>
</body>
</html>
