<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Hacker Green Music Visualizer</title>
<style>
  :root{
    --bg:#03110a; /* very dark green */
    --grid:#0b2d1a;
    --neon:#18ff6d;
    --neon-2:#00f95a;
    --neon-3:#7aff9e;
    --text:#c9ffd9;
  }
  html,body{height:100%;}
  body{
    margin:0; background:radial-gradient(1200px 800px at 50% 65%, #062014 0%, var(--bg) 70%); color:var(--text);
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    overflow:hidden; letter-spacing:.25px;
  }
  /* Layered canvases */
  .stage{position:fixed; inset:0;}
  canvas{position:absolute; inset:0; width:100%; height:100%; display:block;}

  /* UI Panel */
  .hud{
    position:fixed; left:20px; top:20px; right:20px; display:flex; flex-wrap:wrap; gap:12px; align-items:center; z-index:20;
    backdrop-filter: blur(4px);
  }
  .card{
    background: linear-gradient(180deg, rgba(16,55,34,.45), rgba(5,25,15,.45));
    border:1px solid #0f3a23; border-radius:14px; padding:10px 12px; display:flex; align-items:center; gap:10px; box-shadow:0 0 0 1px rgba(0,0,0,.35) inset, 0 8px 24px rgba(0,0,0,.35);
  }
  .card h3{margin:0 8px 0 0; font-size:12px; font-weight:600; color:var(--neon-3); text-shadow:0 0 8px rgba(0,255,128,.25);}
  .btn{cursor:pointer; user-select:none; border:1px solid #0f3a23; background:#0b2d1a; color:var(--text); padding:8px 12px; border-radius:10px; font-weight:700; text-transform:uppercase; letter-spacing:.8px; box-shadow:0 0 12px rgba(0,255,128,.2) inset; transition:transform .1s ease, box-shadow .2s ease, background .2s ease;}
  .btn:hover{box-shadow:0 0 16px rgba(0,255,128,.35) inset;}
  .btn:active{transform:translateY(1px) scale(.99);} 
  input[type="file"]{accent-color:var(--neon);}
  .range{appearance:none; height:8px; border-radius:6px; background:#0c2a19; outline:none; box-shadow:0 0 0 1px #0f3a23 inset;}
  .range::-webkit-slider-thumb{appearance:none; width:18px; height:18px; border-radius:50%; background:var(--neon); box-shadow:0 0 16px var(--neon); border:1px solid #093;}
  .small{font-size:11px; opacity:.8}
  .readout{font-weight:700; color:var(--neon-3)}

  /* Decorative overlays */
  .scanlines{position:fixed; inset:0; pointer-events:none; background-image: repeating-linear-gradient(to bottom, rgba(0,0,0,.0) 0, rgba(0,0,0,.0) 2px, rgba(0,0,0,.12) 3px, rgba(0,255,128,.02) 4px); mix-blend-mode: overlay; z-index:10;}
  .vignette{position:fixed; inset:0; pointer-events:none; background:radial-gradient(ellipse at center, rgba(0,0,0,0) 50%, rgba(0,0,0,.45) 90%); z-index:11}

  /* Corner watermarks */
  .watermark{position:fixed; right:14px; bottom:8px; font-size:11px; color:#71ffb0; opacity:.7; z-index:25; letter-spacing:1.5px}

  /* Logo ring */
  .logo-ring{position:fixed; inset:auto; left:50%; bottom:40px; transform:translateX(-50%); display:flex; gap:14px; z-index:22; filter:drop-shadow(0 0 6px rgba(0,255,128,.35));}
  .logo{width:28px; height:28px; opacity:.85}

  @keyframes glowPulse {0%{filter:drop-shadow(0 0 2px rgba(0,255,128,.4));}50%{filter:drop-shadow(0 0 14px rgba(0,255,128,.7));}100%{filter:drop-shadow(0 0 2px rgba(0,255,128,.4));}}
  .pulsing{animation:glowPulse 2s infinite linear}

  /* Mobile caution */
  @media (max-width: 640px){ .hud{flex-direction:column; align-items:stretch;} .logo-ring{bottom:20px;} }
</style>
</head>
<body>
  <!-- UI / Controls -->
  <div class="hud">
    <div class="card">
      <h3>AUDIO</h3>
      <input id="file" type="file" accept="audio/*" class="btn"/>
      <button id="micBtn" class="btn">Mic</button>
      <button id="toneBtn" class="btn">Test Tone</button>
      <button id="playBtn" class="btn">Play/Pause</button>
    </div>
    <div class="card">
      <h3>VISUALS</h3>
      <label class="small">Intensity <span id="intensityVal" class="readout">1.0</span></label>
      <input id="intensity" type="range" class="range" min="0.5" max="2.0" step="0.01" value="1.0"/>
      <label class="small">Glitch <span id="glitchVal" class="readout">0.25</span></label>
      <input id="glitch" type="range" class="range" min="0" max="1" step="0.01" value="0.25"/>
      <label class="small">Matrix</label>
      <input id="matrixToggle" type="checkbox" checked/>
      <label class="small">Hex</label>
      <input id="hexToggle" type="checkbox" checked/>
    </div>
    <div class="card">
      <h3>METER</h3>
      <div class="small">FPS: <span id="fps" class="readout">0</span></div>
      <div class="small">BASS: <span id="bass" class="readout">0</span></div>
      <div class="small">MID: <span id="mid" class="readout">0</span></div>
      <div class="small">TREBLE: <span id="treble" class="readout">0</span></div>
    </div>
  </div>

  <!-- Logo ring -->
  <div class="logo-ring">
    <svg class="logo" viewBox="0 0 64 64" aria-hidden="true"> <!-- biohazard -->
      <path fill="none" stroke="var(--neon)" stroke-width="3" d="M32 26a6 6 0 1 1 0 12a6 6 0 0 1 0-12Zm0-8c3 0 6 1 8 3c2-3 6-5 10-5c4 0 8 2 10 5l-4 3c-1-2-3-3-6-3c-3 0-6 2-7 5c4 5 6 11 6 17h-5c0-5-2-10-6-13c-2 1-4 2-6 2s-4-1-6-2c-4 3-6 8-6 13H15c0-6 2-12 6-17c-1-3-4-5-7-5c-3 0-5 1-6 3l-4-3c2-3 6-5 10-5c4 0 8 2 10 5c2-2 5-3 8-3Z"/>
    </svg>
    <svg class="logo" viewBox="0 0 64 64" aria-hidden="true"> <!-- radiation -->
      <circle cx="32" cy="32" r="6" fill="none" stroke="var(--neon)" stroke-width="3" />
      <path fill="none" stroke="var(--neon)" stroke-width="3" d="M32 10a22 22 0 0 1 19 11l-13 8a8 8 0 0 0-6-3V10Z"/>
      <path fill="none" stroke="var(--neon)" stroke-width="3" d="M13 21a22 22 0 0 0 0 22l13-8a8 8 0 0 1 0-6L13 21Z"/>
      <path fill="none" stroke="var(--neon)" stroke-width="3" d="M51 43A22 22 0 0 1 32 54V34a8 8 0 0 1 6 3l13 6Z"/>
      <circle cx="32" cy="32" r="22" fill="none" stroke="var(--neon)" stroke-width="2" opacity=".5"/>
    </svg>
    <svg class="logo" viewBox="0 0 64 64" aria-hidden="true"> <!-- skull minimal -->
      <path d="M32 8c10 0 18 8 18 18c0 8-5 12-8 13v7H22v-7c-3-1-8-5-8-13C14 16 22 8 32 8Z" fill="none" stroke="var(--neon)" stroke-width="3"/>
      <circle cx="25" cy="28" r="4" fill="var(--neon)"/>
      <circle cx="39" cy="28" r="4" fill="var(--neon)"/>
      <path d="M26 44h12" stroke="var(--neon)" stroke-width="3"/>
    </svg>
  </div>

  <div class="scanlines"></div>
  <div class="vignette"></div>
  <div class="watermark">SYS:VIZ // TOXIC-GREEN // v1.0</div>

  <!-- Canvases -->
  <div class="stage">
    <canvas id="bg"></canvas>        <!-- matrix rain + hex grid -->
    <canvas id="viz"></canvas>       <!-- main visualizer -->
    <canvas id="hud"></canvas>       <!-- overlays, logo, sparks, readouts -->
  </div>

<script>
(function(){
  const $ = sel => document.querySelector(sel);
  const bg = $('#bg'), viz = $('#viz'), hud = $('#hud');
  const ctxB = bg.getContext('2d');
  const ctxV = viz.getContext('2d');
  const ctxH = hud.getContext('2d');
  let W=innerWidth, H=innerHeight, DPR = Math.min(devicePixelRatio||1, 2);
  function resize(){ W=innerWidth; H=innerHeight; [bg,viz,hud].forEach(c=>{ c.width=W*DPR; c.height=H*DPR; c.style.width=W+'px'; c.style.height=H+'px'; }); [ctxB,ctxV,ctxH].forEach(c=>c.setTransform(DPR,0,0,DPR,0,0)); buildMatrix(); buildHex(); }
  addEventListener('resize', resize, {passive:true}); resize();

  // --------- Audio setup ---------
  let AC, analyser, freq, timeDom, srcNode, micStream, oscChain=null;
  let isPlaying=false; let lastBass=0;
  const fftSize = 2048;
  function ensureAC(){ if(!AC){ AC = new (window.AudioContext||window.webkitAudioContext)(); analyser = AC.createAnalyser(); analyser.fftSize = fftSize; analyser.smoothingTimeConstant = 0.85; freq=new Uint8Array(analyser.frequencyBinCount); timeDom=new Uint8Array(analyser.fftSize); }
  }
  function connectSource(node){ ensureAC(); node.connect(analyser); analyser.connect(AC.destination); }

  $('#file').addEventListener('change', async (e)=>{
    const file = e.target.files[0]; if(!file) return;
    stopAudio(); ensureAC();
    const url = URL.createObjectURL(file);
    const audio = new Audio(); audio.src=url; audio.crossOrigin = 'anonymous'; audio.loop = true; audio.addEventListener('canplay',()=>{ if(srcNode) srcNode.disconnect(); srcNode = AC.createMediaElementSource(audio); connectSource(srcNode); audio.play(); isPlaying=true; });
  });

  $('#micBtn').addEventListener('click', async ()=>{
    try{ stopAudio(); ensureAC(); micStream = await navigator.mediaDevices.getUserMedia({audio:true}); const mic = AC.createMediaStreamSource(micStream); connectSource(mic); isPlaying=true; }
    catch(err){ alert('Mic access failed: '+err.message); }
  });

  function startTone(){ stopAudio(); ensureAC();
    const osc = AC.createOscillator(); const gain = AC.createGain(); const comp = AC.createDynamicsCompressor();
    const lfo = AC.createOscillator(); const lfoGain = AC.createGain();
    osc.type='sawtooth'; osc.frequency.value=90;
    lfo.type='sine'; lfo.frequency.value=2.1; lfoGain.gain.value=40;
    lfo.connect(lfoGain); lfoGain.connect(osc.frequency);

    // Simple kick every ~0.5s
    const kick = AC.createOscillator(); const kGain = AC.createGain();
    kick.type='sine'; kick.frequency.value=120; kGain.gain.value=0; kick.connect(kGain); kGain.connect(gain);
    let kTime=0; function scheduleKick(){ const now = AC.currentTime; if(now>kTime){ kTime=now+0.5; kick.frequency.setValueAtTime(150, kTime); kick.frequency.exponentialRampToValueAtTime(35, kTime+0.25); kGain.gain.setValueAtTime(0.8,kTime); kGain.gain.exponentialRampToValueAtTime(0.001, kTime+0.25); }
      toneRAF = requestAnimationFrame(scheduleKick); }

    osc.connect(gain); gain.connect(comp); comp.connect(analyser); analyser.connect(AC.destination);
    osc.start(); lfo.start(); kick.start();
    let toneRAF=0; scheduleKick();

    oscChain={stop:()=>{ cancelAnimationFrame(toneRAF); try{osc.stop(); kick.stop(); lfo.stop();}catch{} gain.disconnect(); comp.disconnect(); }};
    isPlaying=true;
  }
  $('#toneBtn').addEventListener('click', startTone);

  function stopAudio(){ if(oscChain){ oscChain.stop(); oscChain=null; } if(micStream){ micStream.getTracks().forEach(t=>t.stop()); micStream=null; }
    if(srcNode){ try{srcNode.mediaElement.pause(); srcNode.disconnect();}catch{} srcNode=null; } isPlaying=false; }

  $('#playBtn').addEventListener('click', ()=>{
    if(!AC){ return; }
    if(srcNode && srcNode.mediaElement){ const el = srcNode.mediaElement; if(el.paused){ el.play(); isPlaying=true; } else { el.pause(); isPlaying=false; } }
  });

  // --------- Visual helpers ---------
  const intensityEl=$('#intensity'); const intensityVal=$('#intensityVal');
  const glitchEl=$('#glitch'); const glitchVal=$('#glitchVal');
  intensityEl.addEventListener('input', ()=> intensityVal.textContent=Number(intensityEl.value).toFixed(2));
  glitchEl.addEventListener('input', ()=> glitchVal.textContent=Number(glitchEl.value).toFixed(2));

  function lerp(a,b,t){ return a+(b-a)*t; }
  function clamp(v,min,max){ return Math.max(min, Math.min(max, v)); }
  function map(v, a1,a2, b1,b2){ const t=(v-a1)/(a2-a1); return b1 + clamp(t,0,1)*(b2-b1); }

  // --------- Matrix rain background ---------
  let matrixCols=[], matrixFontSize=16; let matrixChars="0123456789ABCDEF{}[]<>#/*$%&@";
  function buildMatrix(){ matrixFontSize = Math.max(14, Math.floor(W/80)); const cols = Math.ceil(W / matrixFontSize); matrixCols = new Array(cols).fill(0).map(()=>({ y: (Math.random()*H)|0, speed: 2+Math.random()*4 })); }
  function drawMatrix(){ if(!$('#matrixToggle').checked) return; ctxB.save(); ctxB.globalCompositeOperation='source-over'; ctxB.fillStyle='rgba(3,17,10,0.35)'; ctxB.fillRect(0,0,W,H);
    ctxB.font = `${matrixFontSize}px ui-monospace, monospace`; ctxB.textBaseline='top';
    for(let i=0;i<matrixCols.length;i++){
      const x=i*matrixFontSize; const col=matrixCols[i]; const ch = matrixChars[(Math.random()*matrixChars.length)|0];
      ctxB.fillStyle='rgba(0, 255, 128, 0.08)'; ctxB.fillText(ch, x, col.y - matrixFontSize);
      ctxB.fillStyle='rgba(24, 255, 109, 0.65)'; ctxB.fillText(ch, x, col.y);
      col.y += col.speed; if(col.y > H + 20){ col.y = -20; col.speed = 2 + Math.random()*5; }
    }
    ctxB.restore();
  }

  // --------- Hex grid (parallax) ---------
  let hexCache=null; let hexScroll=0;
  function buildHex(){ const off = document.createElement('canvas'); off.width=W; off.height=H; const c=off.getContext('2d');
    const size=28; const h=Math.sin(Math.PI/3)*size; c.strokeStyle='rgba(24,255,109,.18)'; c.lineWidth=1;
    for(let y=-size; y<H+size; y+=h){ for(let x=-size; x<W+size; x+=size*1.5){ const x2 = x + ((Math.round(y/h)%2)? size*.75 : 0); drawHex(c,x2,y,size); } }
    hexCache=off;
  }
  function drawHex(c,x,y,r){ c.beginPath(); for(let i=0;i<6;i++){ const a = i*Math.PI/3; const px=x+r*Math.cos(a), py=y+r*Math.sin(a); if(i===0) c.moveTo(px,py); else c.lineTo(px,py);} c.closePath(); c.stroke(); }
  function renderHex(){ if(!$('#hexToggle').checked) return; ctxB.save(); ctxB.globalAlpha=.7; ctxB.drawImage(hexCache, 0, (hexScroll%H)-H); ctxB.drawImage(hexCache, 0, (hexScroll%H)); ctxB.restore(); hexScroll += 0.2; }

  // --------- Spectrum + waveform visuals ---------
  const sparks=[]; function addSpark(x,y){ sparks.push({x,y,vx:(Math.random()-0.5)*2, vy:-2-Math.random()*2, life:1}); }
  function updateSparks(){ for(let i=sparks.length-1;i>=0;i--){ const s=sparks[i]; s.x+=s.vx; s.y+=s.vy; s.vy+=0.03; s.life-=0.02; if(s.life<=0) sparks.splice(i,1);} }
  function drawSparks(){ ctxV.save(); for(const s of sparks){ ctxV.globalAlpha = s.life; ctxV.fillStyle='rgba(24,255,109,0.9)'; ctxV.fillRect(s.x, s.y, 2,2); } ctxV.restore(); }

  function drawSpectrum(){ if(!analyser) return; analyser.getByteFrequencyData(freq); analyser.getByteTimeDomainData(timeDom);
    // bands
    const N=freq.length; let bassE=0, midE=0, trebE=0;
    for(let i=0;i<N;i++){
      const f = freq[i]; const norm = f/255; const hz = i * (AC.sampleRate/2) / N;
      if(hz<200) bassE += norm; else if(hz<2000) midE += norm; else trebE += norm;
    }
    bassE/= (N*0.25); midE/= (N*0.5); trebE/= (N*0.25);
    const intensity = parseFloat(intensityEl.value);

    // HUD meters
    $('#bass').textContent=(bassE*100|0); $('#mid').textContent=(midE*100|0); $('#treble').textContent=(trebE*100|0);

    // Glitch shake
    const shake = parseFloat(glitchEl.value) * (bassE*2);
    ctxV.setTransform(1,0,0,1, (Math.random()-0.5)*shake*6, (Math.random()-0.5)*shake*6);

    // radial bars
    const cx=W/2, cy=H*0.58; const R = Math.min(W,H)*0.26; const bars=140; const step=Math.floor(N/bars);
    ctxV.save(); ctxV.translate(cx,cy);
    ctxV.rotate((performance.now()*0.00008) + bassE*0.1);

    for(let i=0;i<bars;i++){
      const v=freq[i*step]/255; const len = (R*0.2 + v*R*1.15) * intensity; const a = (i/bars)*Math.PI*2;
      const x = Math.cos(a)*(R*0.8), y=Math.sin(a)*(R*0.8); const x2=Math.cos(a)*(R*0.8+len), y2=Math.sin(a)*(R*0.8+len);
      ctxV.strokeStyle=`rgba(24,255,109, ${0.3+v*0.7})`; ctxV.lineWidth=1.5+v*2.5; ctxV.beginPath(); ctxV.moveTo(x,y); ctxV.lineTo(x2,y2); ctxV.stroke();
      if(v>0.75 && Math.random()<0.08){ addSpark(cx+x2, cy+y2); }
    }
    ctxV.restore();

    // central waveform ring
    ctxV.save(); ctxV.translate(cx, cy);
    const radius = R*0.62 * (1 + bassE*0.05);
    ctxV.beginPath();
    for(let i=0;i<timeDom.length;i++){
      const t = (timeDom[i]-128)/128; const a = (i/timeDom.length)*Math.PI*2; const r = radius + t*34*intensity;
      const px = Math.cos(a)*r, py=Math.sin(a)*r; i===0? ctxV.moveTo(px,py): ctxV.lineTo(px,py);
    }
    ctxV.closePath(); ctxV.strokeStyle='rgba(122,255,158,.9)'; ctxV.lineWidth=1.5; ctxV.stroke();
    ctxV.restore();

    // biohazard logo pulse
    const pulse = 1 + bassE*0.25 + Math.sin(performance.now()*0.003)*0.02;
    drawBiohazard(cx, cy, R*1.05*pulse);

    // sparks
    updateSparks(); drawSparks();

    // bass flash
    if(bassE - lastBass > 0.12){ flash(cx, cy, R*1.3); }
    lastBass = bassE;
  }

  function flash(x,y,r){ ctxH.save(); const grd = ctxH.createRadialGradient(x,y,0,x,y,r); grd.addColorStop(0,'rgba(24,255,109,.35)'); grd.addColorStop(1,'rgba(24,255,109,0)'); ctxH.fillStyle=grd; ctxH.globalCompositeOperation='lighter'; ctxH.beginPath(); ctxH.arc(x,y,r,0,Math.PI*2); ctxH.fill(); ctxH.restore(); }

  // --------- HUD & logo drawing ---------
  function drawBiohazard(cx,cy,r){ ctxH.save(); ctxH.translate(cx,cy); ctxH.rotate(performance.now()*0.0002);
    ctxH.lineWidth=2; ctxH.strokeStyle='rgba(24,255,109,.9)';
    // outer ring
    ctxH.globalAlpha=.9; ctxH.beginPath(); ctxH.arc(0,0,r*1.02,0,Math.PI*2); ctxH.stroke();
    // tri-arms
    for(let i=0;i<3;i++){
      const a=i*2*Math.PI/3; const R=r*0.82; const x=Math.cos(a)*R, y=Math.sin(a)*R; ctxH.beginPath(); ctxH.arc(x,y, r*0.55, a-Math.PI*0.75, a+Math.PI*0.75); ctxH.stroke();
    }
    // center
    ctxH.beginPath(); ctxH.arc(0,0,r*0.22,0,Math.PI*2); ctxH.stroke();
    ctxH.restore();
  }

  // --------- Main loop ---------
  let last=performance.now(), fps=0; function tick(){ const now=performance.now(); const dt=now-last; last=now; fps = lerp(fps, 1000/dt, 0.05); $('#fps').textContent = (fps|0);
    // clear layers
    ctxV.setTransform(1,0,0,1,0,0); ctxV.globalCompositeOperation='source-over'; ctxV.clearRect(0,0,W,H);
    ctxH.setTransform(1,0,0,1,0,0); ctxH.globalCompositeOperation='source-over'; ctxH.fillStyle='rgba(0,0,0,0.06)'; ctxH.fillRect(0,0,W,H); // slight persistence

    renderHex(); drawMatrix(); drawSpectrum();

    // Extra HUD crosshair
    ctxH.save(); ctxH.strokeStyle='rgba(24,255,109,.35)'; ctxH.lineWidth=1; ctxH.beginPath(); ctxH.moveTo(W/2-40,H*0.58); ctxH.lineTo(W/2+40,H*0.58); ctxH.moveTo(W/2,H*0.58-40); ctxH.lineTo(W/2,H*0.58+40); ctxH.stroke(); ctxH.restore();

    requestAnimationFrame(tick);
  }
  tick();
})();
</script>
</body>
</html>
