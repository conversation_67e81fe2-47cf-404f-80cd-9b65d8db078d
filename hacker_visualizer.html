<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HACKER MUSIC VISUALIZER</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            font-family: 'Orbitron', monospace;
            overflow: hidden;
            color: #00ff00;
        }
        
        #canvas {
            display: block;
            background: radial-gradient(circle at center, #001100 0%, #000000 100%);
        }
        
        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 0, 0.9);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px #00ff00;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .controls.hidden {
            transform: translateX(-100%);
            opacity: 0;
            pointer-events: none;
        }

        .toggle-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 101;
            background: rgba(0, 20, 0, 0.9);
            border: 2px solid #00ff00;
            color: #00ff00;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 15px #00ff00;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background: rgba(0, 40, 0, 0.9);
            transform: scale(1.1);
            box-shadow: 0 0 25px #00ff00;
        }

        .toggle-btn.controls-visible {
            transform: translateX(280px);
        }
        
        .controls h2 {
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
            margin-bottom: 15px;
            font-weight: 900;
            letter-spacing: 2px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            color: #00ff00;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        input[type="file"] {
            background: #001100;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px;
            border-radius: 5px;
            width: 100%;
        }
        
        input[type="range"] {
            width: 100%;
            background: #001100;
            border-radius: 5px;
            height: 6px;
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #00ff00;
            border-radius: 50%;
            box-shadow: 0 0 10px #00ff00;
            cursor: pointer;
        }
        
        button {
            background: linear-gradient(45deg, #001100, #003300);
            border: 2px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #003300, #005500);
            box-shadow: 0 0 15px #00ff00;
            transform: translateY(-2px);
        }
        
        .status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 20, 0, 0.9);
            border: 1px solid #00ff00;
            padding: 10px 15px;
            border-radius: 5px;
            color: #00ff00;
            font-size: 12px;
            box-shadow: 0 0 10px #00ff00;
        }
        
        .glitch {
            animation: glitch 2s infinite;
        }
        
        @keyframes glitch {
            0%, 100% { transform: translateX(0); }
            10% { transform: translateX(-2px); }
            20% { transform: translateX(2px); }
            30% { transform: translateX(-1px); }
            40% { transform: translateX(1px); }
            50% { transform: translateX(-2px); }
            60% { transform: translateX(2px); }
            70% { transform: translateX(-1px); }
            80% { transform: translateX(1px); }
            90% { transform: translateX(-2px); }
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>

    <div class="toggle-btn" id="toggleBtn">⚙</div>

    <div class="controls" id="controls">
        <h2 class="glitch">HACKER VISUALIZER</h2>
        <div class="control-group">
            <label>UPLOAD AUDIO FILE</label>
            <input type="file" id="audioFile" accept="audio/*">
        </div>
        <div class="control-group">
            <label>SENSITIVITY: <span id="sensitivityValue">50</span></label>
            <input type="range" id="sensitivity" min="1" max="100" value="50">
        </div>
        <div class="control-group">
            <label>RING COUNT: <span id="ringCountValue">8</span></label>
            <input type="range" id="ringCount" min="3" max="15" value="8">
        </div>
        <div class="control-group">
            <label>BAR COUNT: <span id="barCountValue">64</span></label>
            <input type="range" id="barCount" min="32" max="128" value="64">
        </div>
        <button id="playPause">▶ PLAY</button>
    </div>
    
    <div class="status" id="status">
        STATUS: NO AUDIO LOADED
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Resize canvas
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // Audio variables
        let audioContext = null;
        let analyser = null;
        let audioSource = null;
        let audioElement = null;
        let frequencyData = null;
        let isPlaying = false;
        
        // Visualization parameters
        let sensitivity = 50;
        let ringCount = 8;
        let barCount = 64;
        
        // Animation variables
        let time = 0;
        let bassLevel = 0;
        let midLevel = 0;
        let trebleLevel = 0;

        // UI state
        let controlsVisible = true;
        
        // Control event listeners
        document.getElementById('sensitivity').addEventListener('input', (e) => {
            sensitivity = parseInt(e.target.value);
            document.getElementById('sensitivityValue').textContent = sensitivity;
        });
        
        document.getElementById('ringCount').addEventListener('input', (e) => {
            ringCount = parseInt(e.target.value);
            document.getElementById('ringCountValue').textContent = ringCount;
        });
        
        document.getElementById('barCount').addEventListener('input', (e) => {
            barCount = parseInt(e.target.value);
            document.getElementById('barCountValue').textContent = barCount;
        });

        // Toggle controls visibility
        document.getElementById('toggleBtn').addEventListener('click', () => {
            const controls = document.getElementById('controls');
            const toggleBtn = document.getElementById('toggleBtn');

            controlsVisible = !controlsVisible;

            if (controlsVisible) {
                controls.classList.remove('hidden');
                toggleBtn.classList.add('controls-visible');
                toggleBtn.innerHTML = '⚙';
            } else {
                controls.classList.add('hidden');
                toggleBtn.classList.remove('controls-visible');
                toggleBtn.innerHTML = '👁';
            }
        });
        
        // Audio file handling
        document.getElementById('audioFile').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    analyser = audioContext.createAnalyser();
                    analyser.fftSize = 512;
                    frequencyData = new Uint8Array(analyser.frequencyBinCount);
                }
                
                if (audioElement) {
                    audioElement.pause();
                    audioElement.src = '';
                }
                
                audioElement = new Audio();
                audioElement.src = URL.createObjectURL(file);
                audioElement.loop = true;
                audioElement.crossOrigin = 'anonymous';
                
                if (audioSource) {
                    audioSource.disconnect();
                }
                audioSource = audioContext.createMediaElementSource(audioElement);
                audioSource.connect(analyser);
                analyser.connect(audioContext.destination);
                
                document.getElementById('status').textContent = `LOADED: ${file.name}`;
                document.getElementById('playPause').textContent = '▶ PLAY';
                isPlaying = false;
                
            } catch (error) {
                console.error('Error loading audio:', error);
                document.getElementById('status').textContent = 'ERROR: FAILED TO LOAD AUDIO';
            }
        });
        
        // Play/Pause functionality
        document.getElementById('playPause').addEventListener('click', async () => {
            if (!audioElement) return;
            
            try {
                if (isPlaying) {
                    audioElement.pause();
                    document.getElementById('playPause').textContent = '▶ PLAY';
                    document.getElementById('status').textContent = 'STATUS: PAUSED';
                    isPlaying = false;
                } else {
                    await audioElement.play();
                    if (audioContext.state === 'suspended') {
                        await audioContext.resume();
                    }
                    document.getElementById('playPause').textContent = '⏸ PAUSE';
                    document.getElementById('status').textContent = 'STATUS: PLAYING';
                    isPlaying = true;
                }
            } catch (error) {
                console.error('Error playing audio:', error);
                document.getElementById('status').textContent = 'ERROR: PLAYBACK FAILED';
            }
        });

        // Audio analysis
        function analyzeAudio() {
            if (!analyser || !frequencyData) return;

            analyser.getByteFrequencyData(frequencyData);

            // Calculate frequency bands
            const bassEnd = Math.floor(frequencyData.length * 0.1);
            const midEnd = Math.floor(frequencyData.length * 0.4);

            let bassSum = 0, midSum = 0, trebleSum = 0;

            for (let i = 0; i < bassEnd; i++) {
                bassSum += frequencyData[i];
            }
            bassLevel = (bassSum / bassEnd) / 255.0;

            for (let i = bassEnd; i < midEnd; i++) {
                midSum += frequencyData[i];
            }
            midLevel = (midSum / (midEnd - bassEnd)) / 255.0;

            for (let i = midEnd; i < frequencyData.length; i++) {
                trebleSum += frequencyData[i];
            }
            trebleLevel = (trebleSum / (frequencyData.length - midEnd)) / 255.0;

            // Apply sensitivity
            const sens = sensitivity / 50.0;
            bassLevel = Math.pow(bassLevel, 1.0 - sens * 0.5);
            midLevel = Math.pow(midLevel, 1.0 - sens * 0.5);
            trebleLevel = Math.pow(trebleLevel, 1.0 - sens * 0.5);
        }

        // Hacker-style drawing functions
        function drawMatrix() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Matrix rain effect
            ctx.fillStyle = '#003300';
            ctx.font = '12px Orbitron';
            for (let i = 0; i < 50; i++) {
                const x = Math.random() * canvas.width;
                const y = (time * 2 + Math.random() * 1000) % canvas.height;
                const char = String.fromCharCode(0x30A0 + Math.random() * 96);
                ctx.fillText(char, x, y);
            }
        }

        function drawRings() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const maxRadius = Math.min(canvas.width, canvas.height) * 0.4;

            for (let i = 0; i < ringCount; i++) {
                const progress = i / ringCount;
                const baseRadius = (progress * maxRadius) + 50;

                // Audio-reactive radius
                let audioBoost = 1.0;
                if (i < ringCount * 0.3) audioBoost += bassLevel * 2;
                else if (i < ringCount * 0.7) audioBoost += midLevel * 1.5;
                else audioBoost += trebleLevel * 1.8;

                const radius = baseRadius * audioBoost;

                // Ring color based on frequency
                const intensity = Math.min(1.0, audioBoost - 1.0 + 0.3);
                const alpha = intensity * 0.8;

                // Outer glow
                const gradient = ctx.createRadialGradient(centerX, centerY, radius - 5, centerX, centerY, radius + 5);
                gradient.addColorStop(0, `rgba(0, 255, 0, 0)`);
                gradient.addColorStop(0.5, `rgba(0, 255, 0, ${alpha})`);
                gradient.addColorStop(1, `rgba(0, 255, 0, 0)`);

                ctx.strokeStyle = gradient;
                ctx.lineWidth = 3 + intensity * 5;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();

                // Inner bright ring
                ctx.strokeStyle = `rgba(0, 255, 0, ${alpha * 1.5})`;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function drawBars() {
            if (!frequencyData) return;

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = Math.min(canvas.width, canvas.height) * 0.25;

            const angleStep = (Math.PI * 2) / barCount;

            for (let i = 0; i < barCount; i++) {
                const angle = i * angleStep - Math.PI / 2;
                const freqIndex = Math.floor((i / barCount) * frequencyData.length);
                const amplitude = (frequencyData[freqIndex] / 255.0) * (sensitivity / 50.0);

                const barHeight = amplitude * 150 + 10;
                const startX = centerX + Math.cos(angle) * radius;
                const startY = centerY + Math.sin(angle) * radius;
                const endX = centerX + Math.cos(angle) * (radius + barHeight);
                const endY = centerY + Math.sin(angle) * (radius + barHeight);

                // Bar color based on frequency
                let color;
                if (i < barCount * 0.3) {
                    color = `rgba(0, 255, 100, ${amplitude + 0.3})`;
                } else if (i < barCount * 0.7) {
                    color = `rgba(0, 255, 150, ${amplitude + 0.3})`;
                } else {
                    color = `rgba(100, 255, 0, ${amplitude + 0.3})`;
                }

                // Draw bar with glow
                ctx.strokeStyle = color;
                ctx.lineWidth = 3;
                ctx.shadowColor = '#00ff00';
                ctx.shadowBlur = 10;
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // Reset shadow
                ctx.shadowBlur = 0;
            }
        }

        function drawHUD() {
            // Corner brackets
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;

            const cornerSize = 30;
            const corners = [
                [20, 20], [canvas.width - 20, 20],
                [20, canvas.height - 20], [canvas.width - 20, canvas.height - 20]
            ];

            corners.forEach(([x, y], i) => {
                ctx.beginPath();
                if (i === 0) { // Top-left
                    ctx.moveTo(x, y + cornerSize);
                    ctx.lineTo(x, y);
                    ctx.lineTo(x + cornerSize, y);
                } else if (i === 1) { // Top-right
                    ctx.moveTo(x - cornerSize, y);
                    ctx.lineTo(x, y);
                    ctx.lineTo(x, y + cornerSize);
                } else if (i === 2) { // Bottom-left
                    ctx.moveTo(x, y - cornerSize);
                    ctx.lineTo(x, y);
                    ctx.lineTo(x + cornerSize, y);
                } else { // Bottom-right
                    ctx.moveTo(x - cornerSize, y);
                    ctx.lineTo(x, y);
                    ctx.lineTo(x, y - cornerSize);
                }
                ctx.stroke();
            });

            // Audio levels display
            ctx.fillStyle = '#00ff00';
            ctx.font = '12px Orbitron';
            ctx.fillText(`BASS: ${Math.floor(bassLevel * 100)}%`, canvas.width - 150, canvas.height - 80);
            ctx.fillText(`MID: ${Math.floor(midLevel * 100)}%`, canvas.width - 150, canvas.height - 60);
            ctx.fillText(`TREBLE: ${Math.floor(trebleLevel * 100)}%`, canvas.width - 150, canvas.height - 40);
        }

        // Main render loop
        function render() {
            time += 0.016;

            analyzeAudio();

            // Clear with fade effect
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            drawMatrix();
            drawRings();
            drawBars();
            drawHUD();

            requestAnimationFrame(render);
        }

        // Start the visualization
        render();
    </script>
</body>
</html>
