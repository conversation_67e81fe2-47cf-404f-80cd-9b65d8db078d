<!DOCTYPE html>
<html>
<head>
    <title>Mandelbulb with Spacetime Grid</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        body {
            margin: 0;
            overflow: hidden;
            background: radial-gradient(ellipse at center, #001122 0%, #000511 70%, #000000 100%);
            font-family: 'Orbitron', monospace;
        }
        canvas { width: 100vw; height: 100vh; display: block; }

        .controls {
            position: fixed;
            top: 15px;
            left: 15px;
            color: #87CEEB;
            background: linear-gradient(135deg, rgba(0,5,17,0.95) 0%, rgba(0,20,40,0.9) 100%);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid rgba(135,206,235,0.3);
            box-shadow: 0 8px 32px rgba(135,206,235,0.1);
            backdrop-filter: blur(10px);
            font-weight: 400;
            transition: opacity 0.8s ease-in-out;
        }

        .controls strong {
            font-weight: 900;
            font-size: 16px;
            text-shadow: 0 0 10px rgba(135,206,235,0.5);
        }

        .physics-controls {
            position: fixed;
            color: #87CEEB;
            background: linear-gradient(135deg, rgba(0,5,17,0.95) 0%, rgba(0,20,40,0.9) 100%);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid rgba(135,206,235,0.3);
            box-shadow: 0 8px 32px rgba(135,206,235,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.8s ease-in-out;
            min-width: 280px;
            max-width: 320px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .physics-controls.collapsed {
            transform: translateY(calc(100% - 50px));
            box-shadow: 0 4px 16px rgba(135,206,235,0.2);
        }

        .toggle-button {
            position: absolute;
            top: -40px;
            right: 15px;
            background: linear-gradient(135deg, rgba(0,5,17,0.95) 0%, rgba(0,30,60,0.9) 100%);
            border: 2px solid rgba(135,206,235,0.4);
            color: #87CEEB;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 12px;
            font-weight: 700;
            transition: all 0.3s ease;
            text-shadow: 0 0 5px rgba(135,206,235,0.3);
        }

        .toggle-button:hover {
            background: linear-gradient(135deg, rgba(135,206,235,0.2) 0%, rgba(0,50,100,0.3) 100%);
            border-color: rgba(135,206,235,0.6);
            box-shadow: 0 0 15px rgba(135,206,235,0.3);
            transform: translateY(-2px);
        }

        .slider-container {
            margin: 12px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider-container label {
            min-width: 140px;
            font-size: 12px;
            font-weight: 400;
            text-shadow: 0 0 5px rgba(135,206,235,0.2);
        }

        input[type="range"] {
            width: 160px;
            height: 6px;
            background: linear-gradient(90deg, rgba(135,206,235,0.2) 0%, rgba(135,206,235,0.4) 100%);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #87CEEB 0%, #4682B4 100%);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(135,206,235,0.5);
            transition: all 0.2s ease;
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(135,206,235,0.8);
        }

        input[type="file"] {
            color: #87CEEB;
            background: rgba(135,206,235,0.1);
            border: 1px solid rgba(135,206,235,0.3);
            border-radius: 6px;
            padding: 6px 10px;
            font-family: 'Orbitron', monospace;
            font-size: 11px;
        }

        #quality {
            position: fixed;
            top: 15px;
            right: 15px;
            color: #87CEEB;
            background: linear-gradient(135deg, rgba(0,5,17,0.95) 0%, rgba(0,20,40,0.9) 100%);
            padding: 15px;
            border-radius: 12px;
            border: 2px solid rgba(135,206,235,0.3);
            box-shadow: 0 8px 32px rgba(135,206,235,0.1);
            backdrop-filter: blur(10px);
            font-weight: 700;
            text-shadow: 0 0 10px rgba(135,206,235,0.3);
            transition: opacity 0.8s ease-in-out;
        }

        #pauseButton {
            background: linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%);
            border: 2px solid rgba(135,206,235,0.4);
            color: #87CEEB;
            padding: 8px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            transition: all 0.3s ease;
            text-shadow: 0 0 5px rgba(135,206,235,0.3);
        }

        #pauseButton:hover {
            background: linear-gradient(135deg, rgba(135,206,235,0.2) 0%, rgba(0,50,100,0.3) 100%);
            border-color: rgba(135,206,235,0.6);
            box-shadow: 0 0 15px rgba(135,206,235,0.3);
            transform: translateY(-1px);
        }

        #audioStatus {
            margin-top: 15px;
            font-size: 11px;
            opacity: 0.8;
            font-style: italic;
        }

        /* Keyboard key styling */
        kbd {
            background: rgba(135,206,235,0.2);
            border: 1px solid rgba(135,206,235,0.3);
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'Orbitron', monospace;
            font-size: 10px;
            font-weight: 700;
        }

        /* Subtle glow animation for active elements */
        @keyframes subtle-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(135,206,235,0.2); }
            50% { box-shadow: 0 0 15px rgba(135,206,235,0.4); }
        }

        .physics-controls:not(.collapsed):hover {
            animation: subtle-glow 3s ease-in-out infinite;
        }

        /* Prevent UI overlaps */
        #audioControls {
            max-height: calc(100vh - 140px);
            overflow-y: auto;
            max-width: 320px;
        }

        #mainControls {
            max-height: calc(100vh - 40px);
            overflow-y: auto;
            max-width: 320px;
        }

        /* UI fade out classes */
        .ui-fade-out {
            opacity: 0 !important;
            pointer-events: none;
        }

        .ui-show-temporarily {
            opacity: 1 !important;
            pointer-events: auto;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .physics-controls {
                min-width: 250px;
                padding: 15px;
            }
            .slider-container label {
                min-width: 120px;
                font-size: 11px;
            }
            input[type="range"] {
                width: 120px;
            }

            /* Stack controls vertically on mobile */
            #audioControls {
                right: 15px;
                left: auto;
                bottom: 15px;
                max-width: calc(100vw - 30px);
            }

            #mainControls {
                max-width: calc(100vw - 30px);
            }
        }

        @media (max-width: 1200px) {
            /* Ensure audio controls don't overlap with main controls */
            #audioControls {
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
<div class="controls">
    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
        <span style="font-size: 24px;">🌌</span>
        <strong>SPACETIME MANDELBULB</strong>
    </div>
    <div style="font-size: 11px; opacity: 0.9; line-height: 1.4;">
        <div><kbd>WASD</kbd> Move Camera</div>
        <div><kbd>Mouse</kbd> Look Around</div>
        <div><kbd>Q/E</kbd> Up/Down</div>
        <div><kbd>+/-</kbd> Quality</div>
    </div>
</div>
<div id="quality">
    <div style="font-size: 12px; opacity: 0.8;">RENDER QUALITY</div>
    <div style="font-size: 18px; font-weight: 900;"><span id="qualityValue">60</span>%</div>
</div>
<div class="physics-controls" id="mainControls" style="bottom: 15px; left: 15px;">
    <button class="toggle-button" id="mainToggle">⚙️ PHYSICS</button>
    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
        <span style="font-size: 16px;">⚙️</span>
        <strong style="font-size: 14px;">PHYSICS CONTROLS</strong>
    </div>
    <div class="slider-container">
        <label>⚡ Gravity Strength</label>
        <input type="range" id="gravityStrength" min="0" max="100" value="60">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="gravityValue">60</span>
    </div>
    <div class="slider-container">
        <label>🕸️ Grid Density</label>
        <input type="range" id="gridDensity" min="5" max="50" value="20">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="gridValue">20</span>
    </div>
    <div class="slider-container">
        <label>🌀 Spacetime Warp</label>
        <input type="range" id="spacetimeWarp" min="0" max="100" value="75">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="warpValue">75</span>
    </div>

    <div class="slider-container">
        <label>🔄 Animation Speed</label>
        <input type="range" id="animSpeed" min="0" max="100" value="25">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="animValue">25</span>
    </div>
    <div class="slider-container">
        <label>💡 Back Light</label>
        <input type="range" id="backLight" min="0" max="100" value="60">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="backLightValue">60</span>
    </div>
    <div style="border-top: 1px solid rgba(135,206,235,0.2); margin: 15px 0; padding-top: 15px;">
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
            <span style="font-size: 16px;">�</span>
            <strong style="font-size: 14px;">VISUAL EFFECTS</strong>
        </div>
        <div class="slider-container">
            <label>🔮 Fractal Type</label>
            <input type="range" id="fractalType" min="0" max="4" value="0" step="1">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="fractalValue">Mandelbulb</span>
        </div>
        <div class="slider-container">
            <label>🌈 Color Morph</label>
            <input type="range" id="colorMorph" min="0" max="100" value="30">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="colorValue">30</span>
        </div>
        <div class="slider-container">
            <label>✨ Surface Detail</label>
            <input type="range" id="surfaceDetail" min="0" max="100" value="40">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="surfaceValue">40</span>
        </div>
        <div class="slider-container">
            <label>🌫️ Volumetric Fog</label>
            <input type="range" id="volumetricFog" min="0" max="100" value="25">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="fogValue">25</span>
        </div>
        <div class="slider-container">
            <label>💎 Material Type</label>
            <input type="range" id="materialType" min="0" max="3" value="0" step="1">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="materialValue">Standard</span>
        </div>
    </div>
    <div style="border-top: 1px solid rgba(135,206,235,0.2); margin: 15px 0; padding-top: 15px;">
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
            <span style="font-size: 16px;">�🎥</span>
            <strong style="font-size: 14px;">CAMERA ORBIT</strong>
        </div>
        <div class="slider-container" style="justify-content: center; margin-bottom: 10px;">
            <button id="orbitToggle" style="background: linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%); border: 2px solid rgba(135,206,235,0.4); color: #87CEEB; padding: 8px 20px; border-radius: 8px; cursor: pointer; font-family: 'Orbitron', monospace; font-weight: 700; transition: all 0.3s ease;">🎵 ENABLE ORBIT</button>
        </div>
        <div class="slider-container">
            <label>📏 Orbit Radius</label>
            <input type="range" id="orbitRadius" min="3" max="15" value="6" step="0.5">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="radiusValue">6.0</span>
        </div>
        <div class="slider-container">
            <label>⚡ Orbit Speed</label>
            <input type="range" id="orbitSpeed" min="0" max="100" value="30">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="speedValue">30</span>
        </div>
        <div class="slider-container">
            <label>📐 Orbit Height</label>
            <input type="range" id="orbitHeight" min="-5" max="5" value="0" step="0.5">
            <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="heightValue">0.0</span>
        </div>
    </div>
</div>
<div class="physics-controls" id="audioControls" style="bottom: 15px; left: auto; right: 15px;">
    <button class="toggle-button" id="audioToggle">🎵 AUDIO</button>
    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 15px;">
        <span style="font-size: 16px;">🎵</span>
        <strong style="font-size: 14px;">AUDIO REACTIVE</strong>
    </div>
    <div class="slider-container" style="flex-direction: column; align-items: flex-start; gap: 8px;">
        <label style="min-width: auto;">🎵 Upload Music File</label>
        <input type="file" id="audioFile" accept="audio/*">
    </div>
    <div class="slider-container" style="justify-content: center; margin: 15px 0;">
        <button id="pauseButton">⏸️ PAUSE</button>
    </div>
    <div class="slider-container" style="justify-content: center; margin: 15px 0;">
        <button id="recordButton" style="background: linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%); border: 2px solid rgba(135,206,235,0.4); color: #87CEEB; padding: 8px 20px; border-radius: 8px; cursor: pointer; font-family: 'Orbitron', monospace; font-weight: 700; transition: all 0.3s ease;">🎬 RECORD</button>
    </div>
    <div class="slider-container" style="justify-content: center; margin: 15px 0;">
        <a id="downloadLink" style="display: none; background: linear-gradient(135deg, rgba(0,60,17,0.9) 0%, rgba(0,80,30,0.8) 100%); border: 2px solid rgba(135,235,135,0.4); color: #90EE90; padding: 8px 20px; border-radius: 8px; text-decoration: none; font-family: 'Orbitron', monospace; font-weight: 700; transition: all 0.3s ease;">📥 DOWNLOAD</a>
    </div>
    <div class="slider-container">
        <label>🎶 Music Reactivity</label>
        <input type="range" id="musicReactivity" min="0" max="100" value="70">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="reactivityValue">70</span>
    </div>
    <div class="slider-container">
        <label>🔊 Audio Sensitivity</label>
        <input type="range" id="audioSensitivity" min="1" max="50" value="20">
        <span style="font-size: 10px; opacity: 0.7; min-width: 30px;" id="sensitivityValue">20</span>
    </div>
    <div id="audioStatus">
        <div style="font-size: 10px; opacity: 0.6; margin-bottom: 5px;">STATUS</div>
        <div style="font-size: 11px;">No audio loaded</div>
    </div>
</div>
<canvas id="canvas"></canvas>
<script>
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl');

// Camera and quality state
let cameraPos = [0.0, 0.0, -6.0];
let cameraRot = [0.0, 0.0];
let quality = 0.6;

// Camera orbiting variables
let cameraOrbitEnabled = false;
let cameraOrbitRadius = 6.0;
let cameraOrbitSpeed = 0.3;
let cameraOrbitHeight = 0.0;
let manualCameraControl = true;

// Physics parameters
let time = 0;
let gravityStrength = 0.6;
let gridDensity = 0.2;
let spacetimeWarp = 0.75;

let animSpeed = 0.25;
let backLightIntensity = 0.6;

// Visual effects parameters
let fractalType = 0;
let colorMorph = 0.3;
let surfaceDetail = 0.4;
let volumetricFog = 0.25;
let materialType = 0;

// Audio analysis variables
let audioContext = null;
let analyser = null;
let audioSource = null;
let frequencyData = null;
let musicReactivity = 0.7;
let audioSensitivity = 0.2;
let bassLevel = 0;
let midLevel = 0;
let trebleLevel = 0;

// Recording variables
let mediaRecorder;
let recordedChunks = [];
let isRecording = false;
const recordButton = document.getElementById('recordButton');
const downloadLink = document.getElementById('downloadLink');
let isAudioPaused = false;
let audioElement = null;

// Update physics parameters from sliders with value displays
document.getElementById('gravityStrength').addEventListener('input', (e) => {
    gravityStrength = e.target.value / 100;
    document.getElementById('gravityValue').textContent = e.target.value;
});
document.getElementById('gridDensity').addEventListener('input', (e) => {
    gridDensity = e.target.value / 100;
    document.getElementById('gridValue').textContent = e.target.value;
});
document.getElementById('spacetimeWarp').addEventListener('input', (e) => {
    spacetimeWarp = e.target.value / 100;
    document.getElementById('warpValue').textContent = e.target.value;
});

document.getElementById('animSpeed').addEventListener('input', (e) => {
    animSpeed = e.target.value / 100;
    document.getElementById('animValue').textContent = e.target.value;
});
document.getElementById('backLight').addEventListener('input', (e) => {
    backLightIntensity = e.target.value / 100;
    document.getElementById('backLightValue').textContent = e.target.value;
});
document.getElementById('musicReactivity').addEventListener('input', (e) => {
    musicReactivity = e.target.value / 100;
    document.getElementById('reactivityValue').textContent = e.target.value;
});
document.getElementById('audioSensitivity').addEventListener('input', (e) => {
    audioSensitivity = e.target.value / 100;
    document.getElementById('sensitivityValue').textContent = e.target.value;
});

// Visual effects controls
const fractalNames = ['Mandelbulb', 'Mandelbox', 'Julia Set', 'Menger Sponge', 'Burning Ship'];
document.getElementById('fractalType').addEventListener('input', (e) => {
    fractalType = parseInt(e.target.value);
    document.getElementById('fractalValue').textContent = fractalNames[fractalType];
});

document.getElementById('colorMorph').addEventListener('input', (e) => {
    colorMorph = e.target.value / 100;
    document.getElementById('colorValue').textContent = e.target.value;
});

document.getElementById('surfaceDetail').addEventListener('input', (e) => {
    surfaceDetail = e.target.value / 100;
    document.getElementById('surfaceValue').textContent = e.target.value;
});

document.getElementById('volumetricFog').addEventListener('input', (e) => {
    volumetricFog = e.target.value / 100;
    document.getElementById('fogValue').textContent = e.target.value;
});

const materialNames = ['Standard', 'Metallic', 'Glass', 'Energy'];
document.getElementById('materialType').addEventListener('input', (e) => {
    materialType = parseInt(e.target.value);
    document.getElementById('materialValue').textContent = materialNames[materialType];
});

// Camera orbit controls
document.getElementById('orbitToggle').addEventListener('click', () => {
    cameraOrbitEnabled = !cameraOrbitEnabled;
    const button = document.getElementById('orbitToggle');
    if (cameraOrbitEnabled) {
        button.textContent = '🎵 DISABLE ORBIT';
        button.style.background = 'linear-gradient(135deg, rgba(135,206,235,0.3) 0%, rgba(0,50,100,0.4) 100%)';
        manualCameraControl = false;
    } else {
        button.textContent = '🎵 ENABLE ORBIT';
        button.style.background = 'linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%)';
        manualCameraControl = true;
    }
});

document.getElementById('orbitRadius').addEventListener('input', (e) => {
    cameraOrbitRadius = parseFloat(e.target.value);
    document.getElementById('radiusValue').textContent = e.target.value;
});

document.getElementById('orbitSpeed').addEventListener('input', (e) => {
    cameraOrbitSpeed = e.target.value / 100;
    document.getElementById('speedValue').textContent = e.target.value;
});

document.getElementById('orbitHeight').addEventListener('input', (e) => {
    cameraOrbitHeight = parseFloat(e.target.value);
    document.getElementById('heightValue').textContent = e.target.value;
});

// Toggle controls functionality
document.getElementById('mainToggle').addEventListener('click', () => {
    const controls = document.getElementById('mainControls');
    controls.classList.toggle('collapsed');
});

document.getElementById('audioToggle').addEventListener('click', () => {
    const controls = document.getElementById('audioControls');
    controls.classList.toggle('collapsed');
});

// Pause button functionality
document.getElementById('pauseButton').addEventListener('click', () => {
    const button = document.getElementById('pauseButton');
    if (audioElement) {
        if (isAudioPaused) {
            audioElement.play();
            button.textContent = '⏸️ Pause';
            isAudioPaused = false;
            // UI will fade out automatically via 'play' event listener
        } else {
            audioElement.pause();
            button.textContent = '▶️ Play';
            isAudioPaused = true;
            // UI will fade in automatically via 'pause' event listener
        }
    }
});

// Audio file handling
document.getElementById('audioFile').addEventListener('change', async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
        // Initialize audio context if not already done
        if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 512;
            frequencyData = new Uint8Array(analyser.frequencyBinCount);
        }

        // Stop previous audio if playing
        if (audioElement) {
            audioElement.pause();
            audioElement.src = '';
        }

        // Create HTML5 audio element for better control
        audioElement = new Audio();
        audioElement.src = URL.createObjectURL(file);
        audioElement.loop = false; // Don't loop the song
        audioElement.crossOrigin = 'anonymous';

        // Connect to analyser
        if (audioSource) {
            audioSource.disconnect();
        }
        audioSource = audioContext.createMediaElementSource(audioElement);
        audioSource.connect(analyser);
        analyser.connect(audioContext.destination);

        // Add event listeners for UI fade
        audioElement.addEventListener('play', fadeOutUI);
        audioElement.addEventListener('pause', fadeInUI);
        audioElement.addEventListener('ended', () => {
            fadeInUI();
            document.getElementById('pauseButton').textContent = '▶️ Play';
            document.getElementById('audioStatus').textContent = `Finished: ${file.name}`;
            isAudioPaused = true;
        });

        // Start playing
        await audioElement.play();
        isAudioPaused = false;
        document.getElementById('pauseButton').textContent = '⏸️ Pause';
        document.getElementById('audioStatus').textContent = `Playing: ${file.name}`;

        // Resume audio context if suspended
        if (audioContext.state === 'suspended') {
            await audioContext.resume();
        }

    } catch (error) {
        console.error('Error loading audio:', error);
        document.getElementById('audioStatus').textContent = 'Error loading audio file';
    }
});

// Audio analysis function
function analyzeAudio() {
    if (!analyser || !frequencyData) return;

    analyser.getByteFrequencyData(frequencyData);

    // Calculate frequency band averages
    const bassEnd = Math.floor(frequencyData.length * 0.1);
    const midEnd = Math.floor(frequencyData.length * 0.4);

    let bassSum = 0, midSum = 0, trebleSum = 0;

    // Bass frequencies (0-10% of spectrum)
    for (let i = 0; i < bassEnd; i++) {
        bassSum += frequencyData[i];
    }
    bassLevel = (bassSum / bassEnd) / 255.0;

    // Mid frequencies (10-40% of spectrum)
    for (let i = bassEnd; i < midEnd; i++) {
        midSum += frequencyData[i];
    }
    midLevel = (midSum / (midEnd - bassEnd)) / 255.0;

    // Treble frequencies (40-100% of spectrum)
    for (let i = midEnd; i < frequencyData.length; i++) {
        trebleSum += frequencyData[i];
    }
    trebleLevel = (trebleSum / (frequencyData.length - midEnd)) / 255.0;

    // Apply sensitivity scaling
    bassLevel = Math.pow(bassLevel, 1.0 - audioSensitivity);
    midLevel = Math.pow(midLevel, 1.0 - audioSensitivity);
    trebleLevel = Math.pow(trebleLevel, 1.0 - audioSensitivity);
}

// UI visibility state
let isUIHidden = false;
let uiShowTimeout = null;

// UI fade functions
function fadeOutUI() {
    const uiElements = [
        document.querySelector('.controls'),
        document.getElementById('quality'),
        document.getElementById('mainControls'),
        document.getElementById('audioControls')
    ];

    uiElements.forEach(element => {
        if (element) {
            element.classList.add('ui-fade-out');
            element.classList.remove('ui-show-temporarily');
        }
    });
    isUIHidden = true;
}

function fadeInUI() {
    const uiElements = [
        document.querySelector('.controls'),
        document.getElementById('quality'),
        document.getElementById('mainControls'),
        document.getElementById('audioControls')
    ];

    uiElements.forEach(element => {
        if (element) {
            element.classList.remove('ui-fade-out');
            element.classList.remove('ui-show-temporarily');
        }
    });
    isUIHidden = false;
}

function showUITemporarily() {
    if (!isUIHidden) return; // Don't show temporarily if UI should be visible

    const uiElements = [
        document.querySelector('.controls'),
        document.getElementById('quality'),
        document.getElementById('mainControls'),
        document.getElementById('audioControls')
    ];

    uiElements.forEach(element => {
        if (element) {
            element.classList.add('ui-show-temporarily');
        }
    });

    // Clear existing timeout
    if (uiShowTimeout) {
        clearTimeout(uiShowTimeout);
    }

    // Hide UI again after 3 seconds
    uiShowTimeout = setTimeout(() => {
        uiElements.forEach(element => {
            if (element && isUIHidden) {
                element.classList.remove('ui-show-temporarily');
            }
        });
    }, 3000);
}

// Resize handler
function resize() {
    const width = window.innerWidth * quality;
    const height = window.innerHeight * quality;
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    canvas.width = width;
    canvas.height = height;
    gl.viewport(0, 0, width, height);
}
window.addEventListener('resize', resize);
resize();

const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    }
`;

const fragmentShaderSource = `
    precision mediump float;
    uniform vec2 resolution;
    uniform vec3 cameraPos;
    uniform vec2 cameraRot;
    uniform float time;
    uniform float gravityStrength;
    uniform float gridDensity;
    uniform float spacetimeWarp;

    uniform float animSpeed;
    uniform float bassLevel;
    uniform float midLevel;
    uniform float trebleLevel;
    uniform float musicReactivity;
    uniform float backLightIntensity;
    uniform float fractalType;
    uniform float colorMorph;
    uniform float surfaceDetail;
    uniform float volumetricFog;
    uniform float materialType;

    // Hash function for noise
    float hash(vec3 p) {
        p = fract(p * 0.3183099 + 0.1);
        p *= 17.0;
        return fract(p.x * p.y * p.z * (p.x + p.y + p.z));
    }

    // Multi-fractal distance function with enhanced effects
    float fractalDistance(vec3 pos) {
        // Audio-reactive scaling
        float bassReaction = 1.0 + bassLevel * musicReactivity * 1.5;
        float bassPulse = 1.0 + sin(bassLevel * musicReactivity * 10.0) * bassLevel * musicReactivity * 0.3;
        vec3 scaledPos = pos / (bassReaction * bassPulse);

        float dist = 10000.0;

        // Mandelbulb (fractalType = 0)
        if (fractalType < 0.5) {
            vec3 z = scaledPos;
            float dr = 1.0;
            float r = 0.0;

            float bassPower = bassLevel * musicReactivity * 6.0;
            float bassWave = sin(time * animSpeed + bassLevel * musicReactivity * 20.0) * bassLevel * musicReactivity * 3.0;
            float power = 8.0 + sin(time * animSpeed) * 2.0 + bassPower + bassWave;

            for (int i = 0; i < 8; i++) {
                r = length(z);
                if (r > 2.0) break;

                float theta = acos(z.z / r);
                float phi = atan(z.y, z.x);
                dr = pow(r, power - 1.0) * power * dr + 1.0;

                float zr = pow(r, power);
                theta = theta * power;
                phi = phi * power;

                z = zr * vec3(sin(theta) * cos(phi), sin(theta) * sin(phi), cos(theta));
                z += scaledPos;
            }
            dist = 0.5 * log(r) * r / dr;
        }

        // Mandelbox (fractalType = 1)
        else if (fractalType < 1.5) {
            vec3 z = scaledPos;
            float dr = 1.0;
            float scale = -2.0 + sin(time * animSpeed) * 0.5 + bassLevel * musicReactivity * 2.0;

            for (int i = 0; i < 8; i++) {
                // Box fold
                z = clamp(z, -1.0, 1.0) * 2.0 - z;

                // Sphere fold
                float r2 = dot(z, z);
                if (r2 < 0.25) {
                    z *= 4.0;
                    dr *= 4.0;
                } else if (r2 < 1.0) {
                    z /= r2;
                    dr /= r2;
                }

                z = z * scale + scaledPos;
                dr = dr * abs(scale) + 1.0;
            }
            dist = length(z) / abs(dr);
        }

        // Julia Set (fractalType = 2)
        else if (fractalType < 2.5) {
            vec3 z = scaledPos;
            vec3 c = vec3(0.3 + sin(time * animSpeed) * 0.2, 0.5 + cos(time * animSpeed * 0.7) * 0.2, 0.2);
            c += vec3(bassLevel, midLevel, trebleLevel) * musicReactivity * 0.5;

            for (int i = 0; i < 8; i++) {
                if (dot(z, z) > 4.0) break;
                z = vec3(z.x*z.x - z.y*z.y - z.z*z.z, 2.0*z.x*z.y, 2.0*z.x*z.z) + c;
            }
            dist = length(z) - 2.0;
        }

        // Menger Sponge (fractalType = 3)
        else if (fractalType < 3.5) {
            vec3 z = scaledPos;
            float d = length(max(abs(z) - vec3(1.0), 0.0));

            for (int i = 0; i < 4; i++) {
                vec3 a = mod(z * 3.0, 2.0) - 1.0;
                float r = 1.0 - 3.0 * max(abs(a.x), max(abs(a.y), abs(a.z)));
                d = max(d, r / pow(3.0, float(i + 1)));
                z = abs(z) - 1.0;
            }
            dist = d;
        }

        // Burning Ship (fractalType = 4)
        else {
            vec3 z = scaledPos;
            vec3 c = scaledPos;

            for (int i = 0; i < 8; i++) {
                if (dot(z, z) > 4.0) break;
                z = vec3(z.x*z.x - z.y*z.y, 2.0*abs(z.x*z.y), z.z*z.z) + c;
            }
            dist = length(z) - 2.0;
        }

        return dist * bassReaction * bassPulse;
    }

    // Gravitational field strength
    float gravitationalField(vec3 pos) {
        float dist = length(pos);
        return gravityStrength / (dist * dist + 0.1);
    }

    // Spacetime grid with warping and animation
    float spacetimeGrid(vec3 pos) {
        // Apply gravitational warping to position
        vec3 warpedPos = pos;
        float field = gravitationalField(pos);
        warpedPos += normalize(pos) * field * spacetimeWarp * 2.0;

        // Add animation - flowing spacetime with mid-frequency reactivity
        float midReaction = midLevel * musicReactivity;
        float flowSpeed = time * animSpeed * 3.0 * (1.0 + midReaction);
        float flowIntensity = 0.2 + midReaction * 0.3;

        warpedPos.x += sin(flowSpeed + pos.z * 0.5) * flowIntensity;
        warpedPos.y += cos(flowSpeed * 0.7 + pos.x * 0.3) * flowIntensity * 0.75;
        warpedPos.z += sin(flowSpeed * 0.5 + pos.y * 0.4) * flowIntensity * 0.5;

        // Create animated grid lines with flowing effect
        vec3 animatedPos = warpedPos * gridDensity * 10.0;
        animatedPos += vec3(flowSpeed * 0.5, flowSpeed * 0.3, flowSpeed * 0.4);

        vec3 grid = abs(fract(animatedPos) - 0.5);
        float gridLine = min(min(grid.x, grid.y), grid.z);

        // Make grid lines much thinner and brighter
        gridLine = smoothstep(0.0, 0.005, gridLine);

        return (1.0 - gridLine) * 2.5; // Increased brightness
    }



    vec3 getRayDir(vec2 uv, vec3 camPos, vec2 camRot) {
        vec3 forward = vec3(
            cos(camRot.y) * sin(camRot.x),
            sin(camRot.y),
            cos(camRot.y) * cos(camRot.x)
        );
        vec3 right = normalize(cross(forward, vec3(0.0, 1.0, 0.0)));
        vec3 up = normalize(cross(right, forward));
        return normalize(forward + uv.x * right + uv.y * up);
    }

    vec3 estimateNormal(vec3 p) {
        float d = 0.01;
        return normalize(vec3(
            fractalDistance(p + vec3(d,0,0)) - fractalDistance(p - vec3(d,0,0)),
            fractalDistance(p + vec3(0,d,0)) - fractalDistance(p - vec3(0,d,0)),
            fractalDistance(p + vec3(0,0,d)) - fractalDistance(p - vec3(0,0,d))
        ));
    }

    // Surface texture function
    float surfaceTexture(vec3 p) {
        float noise1 = hash(p * 8.0 + time * animSpeed);
        float noise2 = hash(p * 16.0 - time * animSpeed * 0.5);
        float noise3 = hash(p * 32.0 + time * animSpeed * 2.0);

        float pattern = sin(p.x * 10.0 + time * animSpeed) * sin(p.y * 10.0) * sin(p.z * 10.0);
        float ridges = abs(sin(length(p) * 20.0 + time * animSpeed * 3.0));

        return mix(1.0, noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2 + pattern * 0.3 + ridges * 0.4, surfaceDetail);
    }

    // Advanced material shading
    vec3 calculateMaterial(vec3 pos, vec3 normal, vec3 viewDir, vec3 lightDir, float lightIntensity) {
        vec3 baseColor = vec3(0.4, 0.7, 1.0);

        // Dynamic color morphing
        float colorPhase = time * animSpeed + bassLevel * musicReactivity * 5.0;
        vec3 morphColor1 = vec3(1.0, 0.3, 0.8); // Pink
        vec3 morphColor2 = vec3(0.3, 1.0, 0.4); // Green
        vec3 morphColor3 = vec3(1.0, 0.8, 0.2); // Orange

        vec3 dynamicColor = mix(baseColor,
            mix(morphColor1, mix(morphColor2, morphColor3, sin(colorPhase * 0.7)), sin(colorPhase)),
            colorMorph);

        // Audio-reactive color shifts
        dynamicColor.r += bassLevel * musicReactivity * 0.5;
        dynamicColor.g += midLevel * musicReactivity * 0.5;
        dynamicColor.b += trebleLevel * musicReactivity * 0.5;

        float texture = surfaceTexture(pos);

        // Material types
        if (materialType < 0.5) {
            // Standard material
            return dynamicColor * lightIntensity * texture;
        }
        else if (materialType < 1.5) {
            // Metallic material
            vec3 reflectDir = reflect(-lightDir, normal);
            float spec = pow(max(dot(viewDir, reflectDir), 0.0), 64.0);
            vec3 metalColor = mix(vec3(0.8, 0.9, 1.0), dynamicColor, 0.3);
            return metalColor * (lightIntensity * 0.3 + spec * 0.7) * texture;
        }
        else if (materialType < 2.5) {
            // Glass material
            float fresnel = pow(1.0 - max(dot(normal, viewDir), 0.0), 3.0);
            vec3 glassColor = mix(dynamicColor * 0.2, vec3(1.0), fresnel);
            return glassColor * (lightIntensity * 0.5 + fresnel * 0.5) * texture;
        }
        else {
            // Energy material
            float energy = sin(time * animSpeed * 5.0 + length(pos) * 10.0) * 0.5 + 0.5;
            energy += bassLevel * musicReactivity * 2.0;
            vec3 energyColor = dynamicColor * (1.0 + energy * 2.0);
            return energyColor * texture;
        }
    }

    void main() {
        vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
        vec3 rayDir = getRayDir(uv, cameraPos, cameraRot);

        float t = 0.0;
        vec3 col = vec3(0.0);
        bool hitMandelbulb = false;

        // Volumetric fog accumulation
        vec3 fogColor = vec3(0.0);

        // Raymarching for Fractal
        for(int i = 0; i < 60; i++) {
            vec3 pos = cameraPos + rayDir * t;
            float d = fractalDistance(pos);

            // Volumetric fog sampling
            if (volumetricFog > 0.0) {
                float fogDensity = volumetricFog * 0.01;
                float distanceFromCenter = length(pos);
                float fogSample = fogDensity / (distanceFromCenter * distanceFromCenter + 1.0);

                // Audio-reactive fog
                fogSample *= 1.0 + bassLevel * musicReactivity * 2.0;

                // Fog color based on distance and audio
                vec3 fogTint = vec3(0.3, 0.6, 1.0) + vec3(bassLevel, midLevel, trebleLevel) * musicReactivity;
                fogColor += fogTint * fogSample * 0.1;
            }

            if(abs(d) < 0.01) {
                vec3 normal = estimateNormal(pos);
                vec3 viewDir = normalize(cameraPos - pos);

                // Main light (front/side lighting)
                vec3 mainLight = normalize(vec3(1.0, 1.0, -1.0));
                float mainDiff = max(0.0, dot(normal, mainLight));

                // Back light (behind the fractal for rim lighting)
                vec3 backLight = normalize(vec3(-0.8, 0.5, 1.2));
                float backDiff = max(0.0, dot(normal, backLight));

                // Combine lighting with adjustable back light intensity
                float totalLighting = mainDiff * 0.8 + backDiff * backLightIntensity + 0.2; // ambient

                // Calculate advanced material
                col = calculateMaterial(pos, normal, viewDir, mainLight, totalLighting);
                hitMandelbulb = true;
                break;
            }

            t += d;
            if(t > 15.0) break;
        }

        // Apply volumetric fog
        col = mix(col, fogColor, min(volumetricFog * 0.5, 0.8));

        // If we didn't hit the Mandelbulb, render spacetime grid and stars
        if (!hitMandelbulb) {
            // Sample spacetime grid along the ray with more samples for smoother animation
            float gridIntensity = 0.0;
            for (int i = 0; i < 25; i++) {
                float rayT = float(i) * 0.4 + 1.5;
                vec3 samplePos = cameraPos + rayDir * rayT;
                float grid = spacetimeGrid(samplePos);
                float falloff = 1.0 / (rayT * rayT * 0.08 + 1.0);
                gridIntensity += grid * falloff * 0.08;
            }

            // Enhanced light blue spacetime grid with pulsing effect
            float pulse = 1.0 + sin(time * animSpeed * 4.0) * 0.3;
            vec3 gridColor = vec3(0.4, 0.8, 1.0) * gridIntensity * pulse;

            col = gridColor;
        }

        // Add some atmospheric glow around the Mandelbulb
        float distToCenter = length(cameraPos + rayDir * t);
        float glow = exp(-distToCenter * 0.3) * 0.1;
        col += vec3(0.2, 0.4, 0.8) * glow;

        gl_FragColor = vec4(col, 1.0);
    }
`;

// Create and compile shaders
function createShader(type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error(gl.getShaderInfoLog(shader));
    }
    return shader;
}

const program = gl.createProgram();
gl.attachShader(program, createShader(gl.VERTEX_SHADER, vertexShaderSource));
gl.attachShader(program, createShader(gl.FRAGMENT_SHADER, fragmentShaderSource));
gl.linkProgram(program);
gl.useProgram(program);

// Set up buffers and attributes
const vertices = new Float32Array([-1,-1, 1,-1, -1,1, 1,1]);
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

const position = gl.getAttribLocation(program, 'position');
gl.enableVertexAttribArray(position);
gl.vertexAttribPointer(position, 2, gl.FLOAT, false, 0, 0);

// Set up uniforms
const uniforms = {
    resolution: gl.getUniformLocation(program, 'resolution'),
    cameraPos: gl.getUniformLocation(program, 'cameraPos'),
    cameraRot: gl.getUniformLocation(program, 'cameraRot'),
    time: gl.getUniformLocation(program, 'time'),
    gravityStrength: gl.getUniformLocation(program, 'gravityStrength'),
    gridDensity: gl.getUniformLocation(program, 'gridDensity'),
    spacetimeWarp: gl.getUniformLocation(program, 'spacetimeWarp'),
    animSpeed: gl.getUniformLocation(program, 'animSpeed'),
    bassLevel: gl.getUniformLocation(program, 'bassLevel'),
    midLevel: gl.getUniformLocation(program, 'midLevel'),
    trebleLevel: gl.getUniformLocation(program, 'trebleLevel'),
    musicReactivity: gl.getUniformLocation(program, 'musicReactivity'),
    backLightIntensity: gl.getUniformLocation(program, 'backLightIntensity'),
    fractalType: gl.getUniformLocation(program, 'fractalType'),
    colorMorph: gl.getUniformLocation(program, 'colorMorph'),
    surfaceDetail: gl.getUniformLocation(program, 'surfaceDetail'),
    volumetricFog: gl.getUniformLocation(program, 'volumetricFog'),
    materialType: gl.getUniformLocation(program, 'materialType')
};

// Input handling
const keys = new Set();
window.addEventListener('keydown', e => {
    const key = e.key.toLowerCase();
    keys.add(key);
    if (key === '+' || key === '=') {
        quality = Math.min(1.0, quality + 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
    if (key === '-') {
        quality = Math.max(0.1, quality - 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
});
window.addEventListener('keyup', e => keys.delete(e.key.toLowerCase()));

let lastMouseMove = 0;
canvas.addEventListener('mousemove', e => {
    const now = performance.now();

    // Show UI temporarily on mouse movement during music playback
    if (isUIHidden) {
        showUITemporarily();
    }

    // Camera rotation (only when dragging and manual control is enabled)
    if (now - lastMouseMove > 16 && (e.buttons & 1) && manualCameraControl) {
        cameraRot[0] += e.movementX * 0.01;
        cameraRot[1] = Math.max(-Math.PI/2, Math.min(Math.PI/2, cameraRot[1] - e.movementY * 0.01));
        lastMouseMove = now;
    }
});

// Also listen for mouse movement on the entire document to catch movement outside canvas
document.addEventListener('mousemove', () => {
    if (isUIHidden) {
        showUITemporarily();
    }
});

let lastRender = 0;
function render(now) {
    if (now - lastRender >= 16) {
        // Analyze audio frequencies
        analyzeAudio();

        // Camera movement logic
        if (cameraOrbitEnabled) {
            // Music-reactive camera orbiting
            const baseOrbitSpeed = cameraOrbitSpeed * animSpeed;
            const bassBoost = bassLevel * musicReactivity * 2.0;
            const midBoost = midLevel * musicReactivity * 1.0;
            const trebleBoost = trebleLevel * musicReactivity * 0.5;

            // Calculate orbit angle with music reactivity
            const orbitAngle = time * baseOrbitSpeed + bassBoost * Math.sin(time * 2.0) + midBoost * Math.cos(time * 1.5);

            // Calculate camera position in orbit
            const radiusWithBass = cameraOrbitRadius + bassLevel * musicReactivity * 2.0;
            const heightWithMid = cameraOrbitHeight + midLevel * musicReactivity * 1.5;

            cameraPos[0] = Math.cos(orbitAngle) * radiusWithBass;
            cameraPos[2] = Math.sin(orbitAngle) * radiusWithBass;
            cameraPos[1] = heightWithMid + trebleBoost * Math.sin(time * 4.0);

            // Look at center (Mandelbulb)
            const lookAtX = 0.0;
            const lookAtY = 0.0;
            const lookAtZ = 0.0;

            // Calculate rotation to look at center
            const dx = lookAtX - cameraPos[0];
            const dy = lookAtY - cameraPos[1];
            const dz = lookAtZ - cameraPos[2];
            const distance = Math.sqrt(dx*dx + dy*dy + dz*dz);

            cameraRot[0] = Math.atan2(dx, dz);
            cameraRot[1] = Math.asin(dy / distance);

        } else if (manualCameraControl) {
            // Manual camera controls (WASD)
            const speed = 0.1;
            if (keys.has('w')) {
                cameraPos[0] += Math.sin(cameraRot[0]) * speed;
                cameraPos[2] += Math.cos(cameraRot[0]) * speed;
            }
            if (keys.has('s')) {
                cameraPos[0] -= Math.sin(cameraRot[0]) * speed;
                cameraPos[2] -= Math.cos(cameraRot[0]) * speed;
            }
            if (keys.has('a')) {
                cameraPos[0] -= Math.cos(cameraRot[0]) * speed;
                cameraPos[2] += Math.sin(cameraRot[0]) * speed;
            }
            if (keys.has('d')) {
                cameraPos[0] += Math.cos(cameraRot[0]) * speed;
                cameraPos[2] -= Math.sin(cameraRot[0]) * speed;
            }
            if (keys.has('q')) cameraPos[1] += speed;
            if (keys.has('e')) cameraPos[1] -= speed;
        }

        time = now * 0.001;

        gl.uniform2f(uniforms.resolution, canvas.width, canvas.height);
        gl.uniform3f(uniforms.cameraPos, ...cameraPos);
        gl.uniform2f(uniforms.cameraRot, ...cameraRot);
        gl.uniform1f(uniforms.time, time);
        gl.uniform1f(uniforms.gravityStrength, gravityStrength);
        gl.uniform1f(uniforms.gridDensity, gridDensity);
        gl.uniform1f(uniforms.spacetimeWarp, spacetimeWarp);
        gl.uniform1f(uniforms.animSpeed, animSpeed);
        gl.uniform1f(uniforms.bassLevel, bassLevel);
        gl.uniform1f(uniforms.midLevel, midLevel);
        gl.uniform1f(uniforms.trebleLevel, trebleLevel);
        gl.uniform1f(uniforms.musicReactivity, musicReactivity);
        gl.uniform1f(uniforms.backLightIntensity, backLightIntensity);
        gl.uniform1f(uniforms.fractalType, fractalType);
        gl.uniform1f(uniforms.colorMorph, colorMorph);
        gl.uniform1f(uniforms.surfaceDetail, surfaceDetail);
        gl.uniform1f(uniforms.volumetricFog, volumetricFog);
        gl.uniform1f(uniforms.materialType, materialType);

        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
        lastRender = now;
    }
    requestAnimationFrame(render);
}

// Recording functionality
function toggleRecording() {
    if (!isRecording) {
        startRecording();
    } else {
        stopRecording();
    }
}

// Start screen recording
function startRecording() {
    recordedChunks = [];

    // First ensure audio context is in running state
    if (!audioContext) {
        // Initialize audio context if not already done
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }

    if (audioContext && audioContext.state === 'suspended') {
        audioContext.resume();
    }

    // Get canvas stream
    const canvasStream = canvas.captureStream(60); // 60 FPS
    console.log("Canvas stream tracks:", canvasStream.getTracks().length);

    // Create combined stream starting with canvas
    const combinedStream = new MediaStream();

    // Add video tracks from canvas
    canvasStream.getVideoTracks().forEach(track => {
        combinedStream.addTrack(track);
    });

    // Try to add audio tracks if available
    let audioTracks = [];
    if (audioContext && audioContext.state === 'running') {
        try {
            // Create a MediaStreamDestination to capture audio
            const dest = audioContext.createMediaStreamDestination();

            // Connect the analyser to the destination
            if (analyser) {
                analyser.connect(dest);
            }

            audioTracks = dest.stream.getAudioTracks();
            audioTracks.forEach(track => {
                combinedStream.addTrack(track);
            });
        } catch (e) {
            console.log("Could not capture audio:", e);
        }
    }

    console.log("Audio tracks added:", audioTracks.length);
    console.log("Total tracks in combined stream:", combinedStream.getTracks().length);

    // Use WebM with VP9 codec with optimized settings
    mediaRecorder = new MediaRecorder(combinedStream, {
        mimeType: 'video/webm; codecs=vp9',
        videoBitsPerSecond: 5000000, // 5 Mbps video
        audioBitsPerSecond: 256000   // 256 kbps audio for better quality
    });

    mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
            recordedChunks.push(e.data);
        }
    };

    mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        downloadLink.href = url;
        downloadLink.download = 'mandelbulb-spacetime.webm';
        downloadLink.textContent = '📥 DOWNLOAD WEBM';
        downloadLink.style.display = 'inline-block';

        isRecording = false;
        recordButton.textContent = '🎬 RECORD';
        recordButton.style.background = 'linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%)';

        // Add conversion note
        const conversionNote = document.createElement('div');
        conversionNote.textContent = 'Note: For MP4 conversion, use online tools like CloudConvert';
        conversionNote.style.color = '#888';
        conversionNote.style.fontSize = '12px';
        conversionNote.style.marginTop = '5px';
        conversionNote.style.textAlign = 'center';
        downloadLink.parentNode.insertBefore(conversionNote, downloadLink.nextSibling);
    };

    mediaRecorder.start(1000); // Collect data every second for more reliable recording
    isRecording = true;
    recordButton.textContent = '🛑 STOP RECORDING';
    recordButton.style.background = 'linear-gradient(135deg, rgba(60,5,5,0.9) 0%, rgba(80,20,20,0.8) 100%)';
}

// Stop screen recording
function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        recordButton.textContent = '🎬 RECORD';
        recordButton.style.background = 'linear-gradient(135deg, rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%)';
    }
}

// Initialize recording functionality
function initRecording() {
    recordButton.addEventListener('click', toggleRecording);
}

// Initialize recording
initRecording();

requestAnimationFrame(render);
</script>
</body>
</html>
