<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Pro Music Visualizer — Single File</title>
<style>
  :root{
    --bg:#06070a; --fg:#e6f1ff; --muted:#9fb3c8; --accent:#7cf; --accent2:#f0f; --accent3:#0f8; --warn:#ff7a7a;
  }
  html,body{margin:0;height:100%;background:radial-gradient(1000px 800px at 20% 10%, #0a0d14 0%, #06070a 40%, #020305 100%);color:var(--fg);font-family:system-ui,Segoe UI,Inter,Roboto,Helvetica,Arial,sans-serif;}
  #app{position:fixed;inset:0;display:grid;grid-template-rows:auto 1fr;}
  header{display:flex;gap:.75rem;flex-wrap:wrap;align-items:center;padding:10px 12px;background:linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));backdrop-filter: blur(8px);border-bottom:1px solid rgba(255,255,255,.08);}
  header .title{font-weight:700;letter-spacing:.5px;margin-right:auto;display:flex;align-items:center;gap:.5rem}
  header .dot{width:10px;height:10px;border-radius:50%;background:var(--accent);box-shadow:0 0 12px var(--accent), 0 0 28px var(--accent)}
  button, label.button{appearance:none;border:1px solid rgba(255,255,255,.18);background:rgba(255,255,255,.05);color:var(--fg);padding:8px 12px;border-radius:12px;cursor:pointer;transition:.2s ease;display:inline-flex;align-items:center;gap:.5rem}
  button:hover, label.button:hover{transform:translateY(-1px);background:rgba(255,255,255,.10)}
  button:active{transform:translateY(0)}
  .danger{border-color:#ff8585;background:rgba(255,0,0,.08)}
  .row{display:flex;gap:.5rem;align-items:center;flex-wrap:wrap}
  .sep{width:1px;height:28px;background:rgba(255,255,255,.15);margin:0 6px}
  input[type="range"]{accent-color:var(--accent)}
  .muted{color:var(--muted)}
  .badge{font-size:.8rem;padding:.15rem .5rem;border-radius:999px;background:rgba(255,255,255,.08);border:1px solid rgba(255,255,255,.18)}
  .panel{position:fixed;right:10px;bottom:10px;display:grid;gap:8px;background:rgba(5,7,11,.55);backdrop-filter:blur(8px);border:1px solid rgba(255,255,255,.12);border-radius:14px;padding:10px;min-width:260px}
  .panel h3{margin:.2rem 0 .4rem;font-size:.95rem;color:#cfe1ff}
  .panel .line{display:flex;justify-content:space-between;align-items:center;gap:8px}
  .panel label{display:flex;align-items:center;gap:8px}
  .panel input[type="checkbox"]{transform:scale(1.2)}
  #vis{position:absolute;inset:0;width:100%;height:100%;display:block}
  .hud{position:absolute;left:12px;bottom:12px;display:flex;gap:10px;flex-wrap:wrap}
  .hud .chip{background:rgba(255,255,255,.06);border:1px solid rgba(255,255,255,.14);padding:6px 10px;border-radius:10px}
  .links{margin-left:auto;display:flex;gap:8px;align-items:center}
  .sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}
</style>
</head>
<body>
<div id="app">
  <header>
    <div class="title"><span class="dot" aria-hidden="true"></span> Pro Visualizer</div>
    <div class="row">
      <button id="btnPlay">▶︎ Play</button>
      <label class="button" for="file"><span>📁 Load Audio</span><input id="file" class="sr-only" type="file" accept="audio/*"/></label>
      <button id="btnMic">🎤 Mic</button>
      <div class="sep"></div>
      <button id="btnShuffle">🔀 Randomize</button>
      <div class="row"><span class="muted">Sensitivity</span> <input id="sens" type="range" min="0.2" max="2.5" step="0.05" value="1"/></div>
      <div class="row"><span class="muted">Blur</span> <input id="blur" type="range" min="0" max="1" step="0.01" value="0.25"/></div>
      <div class="row"><span class="muted">Symmetry</span> <input id="sym" type="range" min="1" max="12" step="1" value="6"/></div>
    </div>
    <div class="links">
      <span class="badge" id="bpm">BPM: --</span>
      <span class="badge" id="status">Idle</span>
      <button id="rec" title="Record canvas + audio">⏺ Record</button>
      <button id="stopRec" class="danger" disabled>⏹ Stop</button>
    </div>
  </header>
  <canvas id="vis"></canvas>
  <div class="hud" aria-live="polite">
    <div class="chip" id="fileName">No file loaded</div>
    <div class="chip" id="timeInfo">00:00 / 00:00</div>
  </div>
  <div class="panel" id="panel">
    <h3>Layers</h3>
    <div class="line"><label><input id="lyStar" type="checkbox" checked/> Starfield</label><span class="muted">parallax</span></div>
    <div class="line"><label><input id="lyGrid" type="checkbox" checked/> Holo Grid</label><span class="muted">retro</span></div>
    <div class="line"><label><input id="lyRing" type="checkbox" checked/> Spectrum Ring</label><span class="muted">polar FFT</span></div>
    <div class="line"><label><input id="lyWave" type="checkbox" checked/> Ribbon Wave</label><span class="muted">time domain</span></div>
    <div class="line"><label><input id="lyPart" type="checkbox" checked/> Particles</label><span class="muted">beats</span></div>
    <div class="line"><label><input id="lyHUD" type="checkbox" checked/> HUD Dials</label><span class="muted">gizmos</span></div>
    <div class="line"><label><input id="lyKale" type="checkbox" checked/> Kaleidoscope</label><span class="muted">symmetry</span></div>
    <h3>Theme</h3>
    <div class="line"><label><input name="theme" type="radio" value="neon" checked/> Neon</label>
      <label><input name="theme" type="radio" value="sunset"/> Sunset</label>
      <label><input name="theme" type="radio" value="slate"/> Slate</label>
    </div>
  </div>
</div>

<script>
(() => {
  const $ = (sel) => document.querySelector(sel);
  const canvas = $('#vis');
  const ctx = canvas.getContext('2d');
  const off = document.createElement('canvas');
  const octx = off.getContext('2d');
  const glow = document.createElement('canvas');
  const gctx = glow.getContext('2d');

  const ui = {
    play: $('#btnPlay'), mic: $('#btnMic'), file: $('#file'), shuffle: $('#btnShuffle'),
    sens: $('#sens'), blur: $('#blur'), sym: $('#sym'),
    bpm: $('#bpm'), status: $('#status'), fileName: $('#fileName'), timeInfo: $('#timeInfo'),
    rec: $('#rec'), stopRec: $('#stopRec')
  };

  const layers = {
    star: $('#lyStar'), grid: $('#lyGrid'), ring: $('#lyRing'), wave: $('#lyWave'),
    part: $('#lyPart'), hud: $('#lyHUD'), kale: $('#lyKale')
  };

  const themeRadios = Array.from(document.querySelectorAll('input[name="theme"]'));

  let W=0, H=0, DPR=1; let tStart=performance.now(); let playing=false;

  function resize(){
    DPR = Math.min(2, window.devicePixelRatio || 1);
    const w = canvas.clientWidth = window.innerWidth;
    const h = canvas.clientHeight = window.innerHeight - $('header').offsetHeight;
    canvas.width = (W=w) * DPR; canvas.height = (H=h) * DPR; ctx.setTransform(DPR,0,0,DPR,0,0);
    off.width = W; off.height = H; glow.width = W; glow.height = H;
  }
  window.addEventListener('resize', resize, {passive:true}); resize();

  // ---------- Audio ----------
  const audio = new Audio(); audio.crossOrigin = 'anonymous'; audio.loop = false;
  const AC = window.AudioContext || window.webkitAudioContext; let actx, analyser, srcNode, gain, dest, rec, recorder;
  const FFT_SIZE = 2048; const FREQ_BINS = 512; // use a slice of the FFT for speed
  let freq = new Uint8Array(FFT_SIZE/2), time = new Uint8Array(FFT_SIZE);

  let spectralHistory = []; const HIST_LEN = 43; // ~0.5s at 86fps
  let beatTimes = []; let bpmEstimate = 0; let lastBeatT = 0;

  function setupAudio(){
    if(actx) return; actx = new AC();
    analyser = actx.createAnalyser(); analyser.fftSize = FFT_SIZE; analyser.smoothingTimeConstant = 0.82;
    gain = actx.createGain(); gain.gain.value = 1.0;
    dest = actx.createMediaStreamDestination(); // for recording audio + canvas
    analyser.connect(gain); gain.connect(actx.destination); gain.connect(dest);
  }

  async function useFile(file){
    stopMic(); setupAudio();
    const url = URL.createObjectURL(file); audio.src = url; await audio.play();
    if(srcNode) srcNode.disconnect();
    srcNode = actx.createMediaElementSource(audio); srcNode.connect(analyser);
    playing = true; ui.status.textContent = 'Playing'; ui.fileName.textContent = file.name;
    audio.addEventListener('ended', ()=>{playing=false; ui.status.textContent='Ended';});
  }

  async function useMic(){
    stopFile(); setupAudio();
    const stream = await navigator.mediaDevices.getUserMedia({ audio: { echoCancellation:false, noiseSuppression:false, autoGainControl:false } });
    if(srcNode) srcNode.disconnect();
    srcNode = actx.createMediaStreamSource(stream); srcNode.connect(analyser);
    playing = true; ui.status.textContent = 'Live mic'; ui.fileName.textContent = '🎤 Microphone';
  }

  function stopFile(){ if(!audio.paused){ audio.pause(); } }
  function stopMic(){ /* letting stream close by GC is fine for demo */ }

  ui.file.addEventListener('change', e=>{ const f=e.target.files[0]; if(f) useFile(f); });
  ui.play.addEventListener('click', async ()=>{
    setupAudio();
    if(audio.src){ if(audio.paused){ await audio.play(); playing=true; ui.status.textContent='Playing'; } else { audio.pause(); playing=false; ui.status.textContent='Paused'; } }
    else {
      // Fallback demo: create oscillator if nothing loaded
      const osc = actx.createOscillator(); const mod = actx.createOscillator(); const gainM = actx.createGain();
      mod.frequency.value = 2; gainM.gain.value = 120; mod.connect(gainM); gainM.connect(osc.frequency);
      osc.type='sawtooth'; osc.frequency.value = 110; osc.connect(analyser); osc.start();
      setTimeout(()=>{osc.stop();}, 6000);
      playing=true; ui.status.textContent='Demo tone'; ui.fileName.textContent='Internal demo';
    }
  });
  ui.mic.addEventListener('click', useMic);

  // Recording canvas + audio
  ui.rec.addEventListener('click', ()=>{
    try{
      setupAudio();
      const cvsStream = canvas.captureStream(60);
      const mixed = new MediaStream([ ...cvsStream.getVideoTracks(), ...dest.stream.getAudioTracks() ]);
      recorder = new MediaRecorder(mixed, { mimeType: 'video/webm;codecs=vp9,opus' });
      const chunks=[]; recorder.ondataavailable = e=>{ if(e.data.size) chunks.push(e.data); };
      recorder.onstop = ()=>{
        const blob = new Blob(chunks, {type:'video/webm'});
        const url = URL.createObjectURL(blob);
        const a = Object.assign(document.createElement('a'), { href:url, download:'visualizer_capture.webm' });
        a.click(); URL.revokeObjectURL(url);
      };
      recorder.start(); ui.stopRec.disabled=false; ui.rec.disabled=true; ui.status.textContent='Recording...';
    }catch(err){ alert('Recording not supported in this browser: '+err); }
  });
  ui.stopRec.addEventListener('click', ()=>{ if(recorder?.state==='recording'){ recorder.stop(); ui.stopRec.disabled=true; ui.rec.disabled=false; ui.status.textContent='Playing'; } });

  // ---------- Visual State ----------
  const rng = (n=1)=>Math.random()*n;
  const clamp = (v,a,b)=>Math.max(a,Math.min(b,v));
  let theme = 'neon';
  const palettes = {
    neon:[ '#7df', '#0ff', '#f0f', '#0f8', '#fff' ],
    sunset:[ '#ffcc66', '#ff6699', '#ff9966', '#66ccff', '#ffffff' ],
    slate:[ '#a7b3c6', '#8ad', '#6ef0c1', '#c7d2fe', '#ffffff' ],
  };

  const starfield = Array.from({length:900}, ()=>({x:rng(W), y:rng(H), z:rng(1), s:rng(1)}));
  let particles=[];

  function drawStarfield(energy){
    if(!layers.star.checked) return;
    const pal = palettes[theme];
    for(let i=0;i<starfield.length;i++){
      const s = starfield[i];
      s.x += (0.2 + energy*1.5) * (s.z*0.6+0.4); if(s.x>W) s.x=0, s.y=rng(H), s.z=rng(1);
      const a = 0.35 + 0.65*s.z; const r = 0.3 + s.s*1.8;
      ctx.globalAlpha = a * (0.7 + energy*0.6);
      ctx.fillStyle = pal[0];
      ctx.beginPath(); ctx.arc(s.x, s.y, r, 0, Math.PI*2); ctx.fill();
    }
    ctx.globalAlpha = 1;
  }

  function drawGrid(timeSec){
    if(!layers.grid.checked) return;
    const step = 40; const pal = palettes[theme];
    ctx.save();
    ctx.strokeStyle = pal[1]; ctx.globalAlpha = 0.12; ctx.lineWidth = 1;
    // perspective-ish grid
    const horizon = H*0.45; const lines = 26; const spread = 1.08;
    for(let i=1;i<lines;i++){
      const t = i/lines; const y = horizon + Math.pow(spread, i)*6;
      ctx.beginPath(); ctx.moveTo(0, y); ctx.lineTo(W, y); ctx.stroke();
    }
    ctx.globalAlpha = 0.10; ctx.strokeStyle = pal[2];
    for(let x=-W; x<W*2; x+=step){
      ctx.beginPath(); ctx.moveTo(x+ (Math.sin(timeSec*0.2+x*0.01)*20), horizon+10);
      ctx.lineTo(W/2 + (x-W/2)*1.4, H);
      ctx.stroke();
    }
    ctx.restore();
  }

  function polarSpectrum(energy){
    if(!layers.ring.checked) return;
    const pal = palettes[theme];
    const cx=W/2, cy=H/2, R = Math.min(W,H)*0.22, bands = 160, angleStep = Math.PI*2/bands, sens=parseFloat(ui.sens.value);
    ctx.save(); ctx.translate(cx, cy);
    for(let i=0;i<bands;i++){
      const idx = Math.floor(i*(freq.length/FREQ_BINS));
      const v = (freq[idx]||0)/255 * sens; const a = i*angleStep;
      const r1 = R*(0.95 + 0.25*Math.sin(i*0.15 + energy*10));
      const r2 = r1 + (R*0.55)*Math.pow(v,1.4);
      ctx.beginPath(); ctx.moveTo(Math.cos(a)*r1, Math.sin(a)*r1);
      ctx.lineTo(Math.cos(a)*r2, Math.sin(a)*r2);
      ctx.strokeStyle = pal[ (i>>4)%pal.length ]; ctx.globalAlpha = 0.85; ctx.lineWidth = 2;
      ctx.stroke();
    }
    // inner pulse
    ctx.beginPath(); ctx.arc(0,0, R*(0.94 + energy*0.2), 0, Math.PI*2);
    ctx.strokeStyle = pal[4]; ctx.globalAlpha = 0.25; ctx.lineWidth = 6; ctx.stroke();
    ctx.restore(); ctx.globalAlpha=1;
  }

  function ribbonWave(){
    if(!layers.wave.checked) return;
    const pal = palettes[theme];
    ctx.save();
    const midY = H*0.5; const amp = H*0.18; const sens=parseFloat(ui.sens.value);
    for(let pass=0; pass<3; pass++){
      ctx.beginPath();
      for(let i=0;i<time.length; i++){
        const x = i/time.length * W;
        const v = (time[i]-128)/128 * sens;
        const y = midY + Math.sin(i*0.035 + pass*0.6) * 6 + v*amp*(0.7+pass*0.2);
        if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
      }
      ctx.strokeStyle = pal[(pass+2)%pal.length]; ctx.globalAlpha = 0.6 - pass*0.15; ctx.lineWidth = 2 + pass*1.5; ctx.stroke();
    }
    ctx.restore(); ctx.globalAlpha=1;
  }

  function emitParticles(n, cx, cy){
    for(let i=0;i<n;i++){
      particles.push({ x:cx + rng(40)-20, y:cy + rng(40)-20, vx: (rng(2)-1)*4, vy:(rng(2)-1)*4, life: 1, size: 1+ rng(3)});
    }
  }
  function drawParticles(){
    if(!layers.part.checked) return;
    const pal = palettes[theme];
    for(let i=particles.length-1;i>=0;i--){
      const p = particles[i]; p.x+=p.vx; p.y+=p.vy; p.vy += 0.02; p.life -= 0.012; if(p.life<=0){ particles.splice(i,1); continue; }
      ctx.globalAlpha = Math.max(0,p.life);
      ctx.fillStyle = pal[(i)%pal.length];
      ctx.beginPath(); ctx.arc(p.x,p.y,p.size*(1.5+ (1-p.life)*2),0,Math.PI*2); ctx.fill();
    }
    ctx.globalAlpha=1;
  }

  function hud(energy){
    if(!layers.hud.checked) return;
    const pal = palettes[theme]; const cx=W*0.15, cy=H*0.78; const r=80;
    ctx.save();
    ctx.translate(cx,cy);
    ctx.strokeStyle=pal[0]; ctx.globalAlpha=0.6; ctx.lineWidth=2; ctx.beginPath(); ctx.arc(0,0,r,0,Math.PI*2); ctx.stroke();
    // ticks
    for(let i=0;i<60;i++){
      const a = i/60*Math.PI*2 + performance.now()*0.0006;
      const k = i%5===0?14:8; ctx.beginPath(); ctx.moveTo(Math.cos(a)*(r-k), Math.sin(a)*(r-k)); ctx.lineTo(Math.cos(a)*r, Math.sin(a)*r); ctx.stroke();
    }
    // needle
    ctx.rotate(energy*6.28 + performance.now()*0.001);
    ctx.beginPath(); ctx.moveTo(0,0); ctx.lineTo(r,0); ctx.strokeStyle=pal[2]; ctx.lineWidth=3; ctx.stroke();
    ctx.restore();
  }

  function kaleidoscope(){
    if(!layers.kale.checked) { ctx.drawImage(off,0,0); return; }
    const n = parseInt(ui.sym.value,10); const cx=W/2, cy=H/2; const angle = Math.PI*2/n;
    ctx.save(); ctx.translate(cx,cy);
    for(let i=0;i<n;i++){
      ctx.save(); ctx.rotate(i*angle);
      if(i%2===0){ ctx.scale(1,1); } else { ctx.scale(1,-1); }
      ctx.translate(-cx,-cy);
      ctx.drawImage(off,0,0);
      ctx.restore();
    }
    ctx.restore();
  }

  function glowPass(amount){
    if(amount<=0) return;
    gctx.clearRect(0,0,W,H); gctx.globalCompositeOperation='lighter';
    for(let i=0;i<3;i++){
      const k = 1 + i*amount*3;
      gctx.globalAlpha = 0.12 + i*0.06;
      gctx.drawImage(canvas, 0,0, W, H, -W*(k-1)/2, -H*(k-1)/2, W*k, H*k);
    }
    gctx.globalCompositeOperation='source-over';
    ctx.globalAlpha=0.85; ctx.drawImage(glow,0,0); ctx.globalAlpha=1;
  }

  function formatTime(sec){ const m=Math.floor(sec/60); const s=Math.floor(sec%60); return `${m.toString().padStart(2,'0')}:${s.toString().padStart(2,'0')}`; }

  // ---------- Beat Detection (Spectral Flux) ----------
  function updateBeat(dt){
    const N = 128; // coarse bands
    let flux = 0;
    for(let i=0;i<N;i++){
      const idx = Math.floor(i * (freq.length/N));
      const mag = (freq[idx]||0)/255;
      const prev = spectralHistory.length? spectralHistory[spectralHistory.length-1][i] : 0;
      const d = Math.max(0, mag - prev);
      flux += d;
    }
    // keep history of coarse spectrum
    const coarse = new Array(N);
    for(let i=0;i<N;i++){ const idx = Math.floor(i * (freq.length/N)); coarse[i] = (freq[idx]||0)/255; }
    spectralHistory.push(coarse); if(spectralHistory.length>HIST_LEN) spectralHistory.shift();

    // adaptive threshold over past window
    const TH = 1.5; // multiplier
    const fluxHist = (updateBeat._hist = updateBeat._hist || []);
    fluxHist.push(flux); if(fluxHist.length>60) fluxHist.shift();
    const avg = fluxHist.reduce((a,b)=>a+b,0)/fluxHist.length;

    const now = performance.now()/1000;
    if(flux > avg*TH && now - lastBeatT > 0.18){
      lastBeatT = now; beatTimes.push(now); if(beatTimes.length>10) beatTimes.shift();
      // estimate BPM from last few intervals
      if(beatTimes.length>=4){
        const intervals=[]; for(let i=1;i<beatTimes.length;i++) intervals.push(beatTimes[i]-beatTimes[i-1]);
        const med = intervals.sort((a,b)=>a-b)[Math.floor(intervals.length/2)];
        bpmEstimate = clamp(60/med, 60, 200);
      }
      // emit particles at ring center
      emitParticles(40, W/2, H/2);
    }
  }

  // ---------- Main Loop ----------
  function tick(now){
    requestAnimationFrame(tick);
    const t = (now - tStart) / 1000;

    // Pull fresh audio data if possible
    if(analyser){ analyser.getByteFrequencyData(freq); analyser.getByteTimeDomainData(time); }

    // energy proxy (bass emphasis)
    let energy = 0; for(let i=0;i<64;i++) energy += (freq[i]||0); energy = (energy/ (64*255));
    energy *= parseFloat(ui.sens.value);

    updateBeat(0.016);

    // Offscreen draw of base layers
    octx.clearRect(0,0,W,H);
    // subtle vignette background
    const grad = octx.createRadialGradient(W/2,H/2,Math.min(W,H)*0.1,W/2,H/2,Math.max(W,H)*0.7);
    const pal = palettes[theme];
    grad.addColorStop(0, 'rgba(20,26,36,0.9)'); grad.addColorStop(1, 'rgba(5,7,11,0.9)');
    octx.fillStyle = grad; octx.fillRect(0,0,W,H);

    // draw base layers into offscreen
    // (use ctx temporarily swapped to offscreen context for unified functions)
    const swap = ctx; (ctx = octx);
    drawStarfield(energy);
    drawGrid(t);
    polarSpectrum(energy);
    ribbonWave();
    drawParticles();
    hud(energy);
    ctx = swap;

    // Kaleidoscope + composite to main canvas
    ctx.clearRect(0,0,W,H);
    kaleidoscope();
    glowPass(parseFloat(ui.blur.value));

    // HUD text
    ui.bpm.textContent = `BPM: ${bpmEstimate? Math.round(bpmEstimate): '--'}`;
    if(audio.src){ ui.timeInfo.textContent = `${formatTime(audio.currentTime)} / ${formatTime(audio.duration||0)}`; }
  }
  requestAnimationFrame(tick);

  // ---------- Interactions ----------
  ui.shuffle.addEventListener('click', ()=>{
    // randomize theme and sliders
    const themes = Object.keys(palettes); theme = themes[Math.floor(Math.random()*themes.length)];
    themeRadios.forEach(r=>{ r.checked = (r.value===theme); });
    ui.sens.value = (0.6 + Math.random()*1.6).toFixed(2);
    ui.blur.value = (Math.random()*0.5).toFixed(2);
    ui.sym.value = (2 + Math.floor(Math.random()*10));
  });
  themeRadios.forEach(r=>r.addEventListener('change', ()=>{ if(r.checked) theme = r.value; }));
})();
</script>
</body>
</html>
