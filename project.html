<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Mandelbulb — Blue-Green, 140 BPM Sync</title>
<style>
  :root{
    --bg:#03060a;
    --accent:#7feee6; /* light blue-green */
    --panel: rgba(0,0,0,0.45);
  }
  html,body{height:100%;margin:0;background:var(--bg);color:#dff;font-family:Inter,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;overflow:hidden}
  canvas{display:block;width:100vw;height:100vh;}
  .ui{
    position:fixed;left:12px;top:12px;z-index:20;background:var(--panel);padding:10px;border-radius:8px;border:1px solid rgba(127,255,230,0.08);
    backdrop-filter: blur(6px);max-width:360px;
  }
  .row{display:flex;gap:8px;align-items:center;margin-bottom:8px}
  label{font-size:12px;color:#9fc;min-width:76px}
  input[type="range"]{width:180px}
  button,input[type=file]{background:transparent;border:1px solid rgba(127,255,230,0.12);color:var(--accent);padding:6px 8px;border-radius:6px;cursor:pointer}
  .small{font-size:12px;color:#9aa}
  .status{font-size:12px;color:#9aa;margin-top:6px}
</style>
</head>
<body>
<canvas id="gl"></canvas>

<div class="ui" role="region" aria-label="controls">
  <div class="row">
    <input id="file" type="file" accept="audio/*">
    <button id="mic">🎤 Mic</button>
    <button id="play">Pause</button>
  </div>

  <div class="row">
    <label>Lock BPM</label>
    <input id="bpm" type="range" min="40" max="240" value="140">
    <div class="small" id="bpmLabel">140</div>
  </div>

  <div class="row">
    <label>Beat Strength</label>
    <input id="bStrength" type="range" min="0" max="2.5" step="0.01" value="1.0">
    <div class="small" id="bStrLabel">1.00</div>
  </div>

  <div class="row">
    <label>Quality</label>
    <input id="quality" type="range" min="40" max="220" value="120">
    <div class="small" id="qLabel">120</div>
  </div>

  <div class="row">
    <label>Sensitivity</label>
    <input id="sens" type="range" min="0.4" max="4.0" step="0.01" value="1.6">
    <div class="small" id="sLabel">1.60</div>
  </div>

  <div class="status">
    Source: <span id="src">none</span> · Beat: <span id="beatHere">no</span>
    <div class="small">Tip: load an MP3 or use mic. If it stutters, lower Quality.</div>
  </div>
</div>

<!-- Fragment shader (Mandelbulb) -->
<script id="frag" type="x-shader/x-fragment">
precision highp float;
uniform vec2 u_resolution;
uniform float u_time;
uniform float u_bpm;         // BPM lock
uniform float u_beat;        // beat envelope 0..1
uniform float u_strength;    // beat strength multiplier
uniform float u_steps;       // raymarch steps
uniform vec3 u_camOffset;    // camera nudges

#define MAX_DIST 200.0
#define SURF_DIST 0.0009

// Distance estimator for Mandelbulb (power modulated by beat)
float mandelbulbDE(vec3 pos) {
  vec3 z = pos;
  float dr = 1.0;
  float r = 0.0;
  float beat = clamp(u_beat, 0.0, 1.0);
  float power = 8.0 + 2.0 * beat * u_strength; // base power 8, small modulation
  for(int i=0;i<14;i++){
    r = length(z);
    if (r>2.0) break;
    float theta = acos(z.z / r);
    float phi   = atan(z.y, z.x);
    float zr    = pow(r, power);
    float newTheta = theta * power;
    float newPhi = phi * power;
    vec3 zrVec = zr * vec3(sin(newTheta)*cos(newPhi), sin(newTheta)*sin(newPhi), cos(newTheta));
    z = zrVec + pos;
    dr = pow(r, power-1.0) * power * dr + 1.0;
  }
  return 0.5 * log(r) * r / dr;
}

vec3 calcNormal(vec3 p){
  float e = 0.0008;
  float d = mandelbulbDE(p);
  vec3 n = vec3(
    mandelbulbDE(p + vec3(e,0,0)) - d,
    mandelbulbDE(p + vec3(0,e,0)) - d,
    mandelbulbDE(p + vec3(0,0,e)) - d
  );
  return normalize(n);
}

float softShadow(vec3 ro, vec3 rd, float mint, float maxt, float k){
  float res = 1.0;
  float t = mint;
  for(int i=0;i<60;i++){
    float h = mandelbulbDE(ro + rd * t);
    if (h < 0.0001) return 0.0;
    res = min(res, k * h / t);
    t += clamp(h, 0.02, 0.5);
    if (t > maxt) break;
  }
  return clamp(res, 0.0, 1.0);
}

vec3 blueGreenPalette(float v){
  // single-tone blue-green, mapped for highlights and deep shadows
  vec3 base = vec3(0.02, 0.04, 0.06); // deep background tint
  vec3 accent = vec3(0.48, 0.95, 0.88); // light blue-green accent (normalized)
  return mix(base, accent, smoothstep(0.0, 1.0, v));
}

void main(){
  vec2 uv = (gl_FragCoord.xy - 0.5 * u_resolution.xy) / u_resolution.y;
  float t = u_time;

  // BPM phase (helps keep a regular rhythmic motion locked to bpm)
  float phase = 6.28318530718 * (u_bpm / 60.0) * t;
  float bpmPulse = pow(max(0.0, sin(phase)), 3.0); // sharpened pulse

  // combine automatic beat (u_beat) and bpmPulse gently
  float beatEnv = clamp(u_beat + 0.55 * bpmPulse, 0.0, 1.0);

  // camera: deep zoom + subtle rotation; zoom responds to beatEnv
  float zoomBase = 3.0;
  float zoomPulse = 1.0 + 0.18 * beatEnv * u_strength;
  float camSpin = 0.08 * t * (1.0 + 0.25 * beatEnv);
  vec3 camPos = vec3(zoomBase * zoomPulse * cos(camSpin), 1.12 * zoomPulse, zoomBase * zoomPulse * sin(camSpin)) + u_camOffset;
  vec3 target = vec3(0.0);
  vec3 forward = normalize(target - camPos);
  vec3 right = normalize(cross(vec3(0.0,1.0,0.0), forward));
  vec3 up = cross(forward, right);
  vec3 rd = normalize(uv.x * right + uv.y * up + 1.45 * forward);

  // raymarch
  float total = 0.0;
  float d = 0.0;
  vec3 p;
  float maxSteps = clamp(u_steps, 40.0, 280.0);
  for (int i = 0; i < 200; i++) {
    if (float(i) >= maxSteps) break;
    p = camPos + rd * total;
    d = mandelbulbDE(p);
    total += d;
    if (d < SURF_DIST || total > MAX_DIST) break;
  }

  vec3 color = vec3(0.0);
  if (total < MAX_DIST) {
    vec3 pos = camPos + rd * total;
    vec3 n = calcNormal(pos);
    vec3 lightDir = normalize(vec3(0.35, 0.75, -0.55));
    float diff = clamp(dot(n, lightDir), 0.0, 1.0);
    vec3 view = normalize(camPos - pos);
    vec3 halfv = normalize(view + lightDir);
    float spec = pow(max(dot(n, halfv), 0.0), 36.0);
    float sh = softShadow(pos + n*0.01, lightDir, 0.02, 5.0, 40.0);
    float iso = length(pos) * 0.12;
    vec3 pal = blueGreenPalette(iso * (0.9 + 0.8 * beatEnv));
    float emission = 0.10 + 0.85 * pow(beatEnv, 2.0);
    color = pal * (0.14 + 0.95 * diff * sh) + pal * emission + vec3(spec * 1.4);
    float rim = pow(1.0 - max(dot(view, n), 0.0), 3.0);
    color += vec3(0.45,0.65,0.75) * rim * 0.35 * beatEnv;
  } else {
    // background: subtle dark gradient with tiny beat shimmer
    float bg = 0.03 + 0.04 * sin(0.9 * u_time) + 0.02 * beatEnv;
    color = mix(vec3(0.005,0.01,0.015), vec3(0.01,0.02,0.03), bg);
  }

  // tone mapping & gamma
  color = pow(clamp(color, 0.0, 3.0), vec3(0.85));
  gl_FragColor = vec4(color, 1.0);
}
</script>

<script>
(() => {
  // WebGL init
  const canvas = document.getElementById('gl');
  const gl = canvas.getContext('webgl', { preserveDrawingBuffer: true });
  if (!gl) { alert('WebGL not available'); return; }

  // compile helpers
  function compile(type, src) {
    const s = gl.createShader(type);
    gl.shaderSource(s, src);
    gl.compileShader(s);
    if (!gl.getShaderParameter(s, gl.COMPILE_STATUS)) {
      console.error(gl.getShaderInfoLog(s));
      throw new Error('Shader compile error');
    }
    return s;
  }

  const vsSrc = `attribute vec2 position; void main(){ gl_Position = vec4(position,0,1); }`;
  const fsSrc = document.getElementById('frag').textContent;
  const vs = compile(gl.VERTEX_SHADER, vsSrc);
  const fs = compile(gl.FRAGMENT_SHADER, fsSrc);
  const program = gl.createProgram();
  gl.attachShader(program, vs);
  gl.attachShader(program, fs);
  gl.linkProgram(program);
  if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
    console.error(gl.getProgramInfoLog(program));
    throw new Error('Program link error');
  }
  gl.useProgram(program);

  // fullscreen quad
  const quad = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, quad);
  gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-1,-1, 1,-1, -1,1, 1,1]), gl.STATIC_DRAW);
  const posLoc = gl.getAttribLocation(program, 'position');
  gl.enableVertexAttribArray(posLoc);
  gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 0, 0);

  // uniforms
  const u_resolution = gl.getUniformLocation(program, 'u_resolution');
  const u_time = gl.getUniformLocation(program, 'u_time');
  const u_bpm = gl.getUniformLocation(program, 'u_bpm');
  const u_beat = gl.getUniformLocation(program, 'u_beat');
  const u_strength = gl.getUniformLocation(program, 'u_strength');
  const u_steps = gl.getUniformLocation(program, 'u_steps');
  const u_camOffset = gl.getUniformLocation(program, 'u_camOffset');

  // hi-dpi resize
  function resize() {
    const dpr = Math.min(window.devicePixelRatio || 1, 2.0);
    canvas.width = Math.floor(window.innerWidth * dpr);
    canvas.height = Math.floor(window.innerHeight * dpr);
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    gl.viewport(0, 0, canvas.width, canvas.height);
    gl.uniform2f(u_resolution, canvas.width, canvas.height);
  }
  window.addEventListener('resize', resize);
  resize();

  // UI elements & state
  const fileEl = document.getElementById('file');
  const micBtn = document.getElementById('mic');
  const playBtn = document.getElementById('play');
  const bpmEl = document.getElementById('bpm');
  const bpmLabel = document.getElementById('bpmLabel');
  const bStr = document.getElementById('bStrength');
  const bStrLabel = document.getElementById('bStrLabel');
  const quality = document.getElementById('quality');
  const qLabel = document.getElementById('qLabel');
  const sens = document.getElementById('sens');
  const sLabel = document.getElementById('sLabel');
  const srcLabel = document.getElementById('src');
  const beatHere = document.getElementById('beatHere');

  let BPM = +bpmEl.value;
  let beatStrength = +bStr.value;
  let steps = +quality.value;
  let sensitivity = +sens.value;

  bpmEl.addEventListener('input', () => { BPM = +bpmEl.value; bpmLabel.textContent = BPM; });
  bStr.addEventListener('input', () => { beatStrength = +bStr.value; bStrLabel.textContent = beatStrength.toFixed(2); });
  quality.addEventListener('input', () => { steps = +quality.value; qLabel.textContent = steps; });
  sens.addEventListener('input', () => { sensitivity = +sens.value; sLabel.textContent = sensitivity.toFixed(2); });

  playBtn.addEventListener('click', () => {
    running = !running;
    playBtn.textContent = running ? 'Pause' : 'Play';
  });

  let running = true;

  // audio analysis & beat detection
  let audioCtx = null;
  let analyser = null;
  let dataArr = null;
  let sourceNode = null;
  let audioEl = null;
  let micStream = null;

  function ensureAudio() {
    if (!audioCtx) audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  }
  function createAnalyser(fft = 2048) {
    ensureAudio();
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = fft;
    analyser.smoothingTimeConstant = 0.86;
    dataArr = new Uint8Array(analyser.frequencyBinCount);
  }

  function connectSource(node) {
    if (!analyser) createAnalyser();
    try { node.connect(analyser); } catch(e){}
    try { analyser.connect(audioCtx.destination); } catch(e){}
    sourceNode = node;
  }

  async function loadFile(file) {
    stopAudio();
    createAnalyser(2048);
    audioEl = new Audio(URL.createObjectURL(file));
    audioEl.crossOrigin = 'anonymous';
    audioEl.loop = true;
    await audioEl.play().catch(()=>{});
    const src = audioCtx.createMediaElementSource(audioEl);
    connectSource(src);
    srcLabel.textContent = file.name;
    startBeat();
  }

  micBtn.addEventListener('click', async () => {
    stopAudio();
    try {
      micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      createAnalyser(2048);
      const src = audioCtx.createMediaStreamSource(micStream);
      connectSource(src);
      srcLabel.textContent = 'microphone';
      startBeat();
    } catch (e) {
      console.error(e);
      srcLabel.textContent = 'mic error';
    }
  });

  fileEl.addEventListener('change', (e) => {
    const f = e.target.files && e.target.files[0];
    if (f) loadFile(f);
  });

  function stopAudio() {
    if (audioEl) { try { audioEl.pause(); audioEl.src = ''; } catch(e){} audioEl = null; }
    if (sourceNode && sourceNode.disconnect) try { sourceNode.disconnect(); } catch(e){}
    if (micStream) { micStream.getTracks().forEach(t => t.stop()); micStream = null; }
    // leave audioCtx open to avoid permissions churn
  }

  // beat detection (bass energy onset)
  let energyH = [];
  const HSIZE = 44;
  let lastBeat = 0;
  let beatEnv = 0.0;
  let beatFlag = false;
  let onsetTimes = [];

  function freqToIndex(freq) {
    if (!audioCtx || !analyser) return 0;
    const nyq = audioCtx.sampleRate / 2;
    const bins = analyser.frequencyBinCount;
    return Math.min(bins - 1, Math.floor(freq / nyq * bins));
  }

  function startBeat(){
    energyH = [];
    onsetTimes = [];
    lastBeat = 0;
    beatEnv = 0;
  }

  function analyze() {
    if (!analyser || !dataArr) return 0;
    analyser.getByteFrequencyData(dataArr);
    const iMax = Math.max(2, freqToIndex(200)); // bass region up to ~200Hz
    let sum = 0;
    for (let i = 0; i <= iMax; i++) sum += dataArr[i];
    const energy = sum / (iMax + 1);
    energyH.push(energy);
    if (energyH.length > HSIZE) energyH.shift();
    let mean = 0;
    for (let v of energyH) mean += v;
    mean /= (energyH.length || 1);
    let varr = 0;
    for (let v of energyH) varr += (v-mean)*(v-mean);
    varr /= (energyH.length || 1);
    const std = Math.sqrt(varr);
    const K = 1.15 * sensitivity;
    const threshold = mean + K * std + 1.0;
    const now = performance.now();
    const minGap = 115; // ms refractory
    let isBeat = false;
    if (energy > threshold && (now - lastBeat) > minGap) {
      isBeat = true;
      lastBeat = now;
      onsetTimes.push(now);
      if (onsetTimes.length > 64) onsetTimes.shift();
    }
    if (isBeat) {
      beatEnv = Math.max(beatEnv, Math.min(1.0, (energy - mean) / (255 - mean)));
      beatFlag = true;
      beatHere.textContent = 'yes';
    } else {
      beatEnv *= 0.93; // decay
      beatFlag = false;
      beatHere.textContent = 'no';
    }
    // BPM auto estimate for info (not forcing)
    if (onsetTimes.length >= 4) {
      let deltas = [];
      for (let i = 1; i < onsetTimes.length; i++) deltas.push(onsetTimes[i] - onsetTimes[i-1]);
      deltas.sort((a,b) => a-b);
      const med = deltas[Math.floor(deltas.length/2)];
      const est = Math.round(60000 / med);
      // we do not override user BPM, just display (optional)
      //document.getElementById('autoBpm').textContent = est;
    }
    // return envelope multiplied by beatStrength slider
    return Math.min(1.0, Math.pow(beatEnv, 0.9) * beatStrength);
  }

  // render loop
  let start = performance.now();
  let camOffset = [0,0,0];

  // pointer drag to nudge camera
  let dragging = false, lastPos = [0,0];
  canvas.addEventListener('pointerdown', e => { dragging = true; lastPos = [e.clientX, e.clientY]; canvas.setPointerCapture(e.pointerId); });
  window.addEventListener('pointerup', () => dragging = false);
  window.addEventListener('pointermove', e => {
    if (!dragging) return;
    const dx = (e.clientX - lastPos[0]) / canvas.width;
    const dy = (e.clientY - lastPos[1]) / canvas.height;
    lastPos = [e.clientX, e.clientY];
    camOffset[0] += dx * 2.0;
    camOffset[1] -= dy * 2.0;
  });

  function render(){
    requestAnimationFrame(render);
    const now = performance.now();
    const t = (now - start)/1000;
    // analyze audio -> beat value
    const beatVal = analyze();
    // BPM slider influences shader rhythmic phase; keep value
    const bpm = BPM;
    // pass uniforms
    gl.uniform1f(u_time, t);
    gl.uniform1f(u_bpm, bpm);
    gl.uniform1f(u_beat, beatVal);
    gl.uniform1f(u_strength, beatStrength);
    gl.uniform1f(u_steps, steps);
    gl.uniform3f(u_camOffset, camOffset[0], camOffset[1], camOffset[2]);
    // draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  // prepare initial uniforms
  gl.uniform1f(u_bpm, BPM);
  gl.uniform1f(u_beat, 0.0);
  gl.uniform1f(u_strength, beatStrength);
  gl.uniform1f(u_steps, steps);
  gl.uniform3f(u_camOffset, 0,0,0);

  // link to UI variables for live update
  setInterval(() => {
    gl.uniform1f(u_bpm, BPM);
    gl.uniform1f(u_strength, beatStrength);
    gl.uniform1f(u_steps, steps);
  }, 200);

  // start the render loop
  requestAnimationFrame(render);

  // helpers to get uniform locations (defined above but declared after compile)
  const u_time = gl.getUniformLocation(program, 'u_time');
  const u_bpm = gl.getUniformLocation(program, 'u_bpm');
  const u_beat = gl.getUniformLocation(program, 'u_beat');
  const u_strength = gl.getUniformLocation(program, 'u_strength');
  const u_steps = gl.getUniformLocation(program, 'u_steps');
  const u_camOffset = gl.getUniformLocation(program, 'u_camOffset');
  const u_resolution = gl.getUniformLocation(program, 'u_resolution');

  // set resolution uniform now (and on resize)
  function setResolution() {
    const dpr = Math.min(window.devicePixelRatio || 1, 2.0);
    canvas.width = Math.floor(window.innerWidth * dpr);
    canvas.height = Math.floor(window.innerHeight * dpr);
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    gl.viewport(0,0,canvas.width,canvas.height);
    gl.uniform2f(u_resolution, canvas.width, canvas.height);
  }
  window.addEventListener('resize', setResolution);
  setResolution();

  // reconnect audio on file input / mic
  fileEl.addEventListener('change', (e) => {
    const f = e.target.files && e.target.files[0];
    if (!f) return;
    (async ()=> {
      ensureAudio();
      stopAudio();
      createAnalyser(2048);
      audioEl = new Audio(URL.createObjectURL(f));
      audioEl.crossOrigin = 'anonymous';
      audioEl.loop = true;
      await audioEl.play().catch(()=>{});
      const src = audioCtx.createMediaElementSource(audioEl);
      connectSource(src);
      srcLabel.textContent = f.name;
      startBeat();
    })();
  });

  micBtn.addEventListener('click', async () => {
    ensureAudio();
    stopAudio();
    try {
      micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      createAnalyser(2048);
      const src = audioCtx.createMediaStreamSource(micStream);
      connectSource(src);
      srcLabel.textContent = 'microphone';
      startBeat();
    } catch(e){
      srcLabel.textContent = 'mic failed';
    }
  });

  // small resume for autoplay policies
  document.addEventListener('click', async function resumeOnce() {
    if (audioCtx && audioCtx.state === 'suspended') try { await audioCtx.resume(); } catch(e){}
    document.removeEventListener('click', resumeOnce);
  });

  // small UI bindings
  fileEl.title = 'Load MP3 or other audio';
})();
})();
</script>
</body>
</html>
