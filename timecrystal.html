<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>3D Symmetrical Time Sculpture — single file</title>
<style>
  :root{
    --panel: rgba(0,0,0,0.45);
    --txt: #e9eef8;
    --muted: #9fb0c8;
  }
  html,body{height:100%;margin:0;background:#07101a;font-family:Inter,ui-sans-serif,system-ui,Segoe UI,Roboto,Helvetica,Arial;}
  #wrap{display:flex;height:100vh;gap:12px;padding:12px;box-sizing:border-box;}
  canvas{flex:1;border-radius:12px;box-shadow:0 12px 40px rgba(0,0,0,0.6);display:block;background:#000;}
  .panel{width:360px;background:var(--panel);border-radius:12px;padding:14px;color:var(--txt);box-sizing:border-box;backdrop-filter:blur(6px);overflow:auto;}
  h1{margin:0 0 8px 0;font-size:18px;}
  .row{display:flex;align-items:center;gap:8px;margin:8px 0;}
  label{width:130px;font-size:13px;color:var(--muted);}
  input[type=range]{flex:1;}
  select,button{background:transparent;border:1px solid rgba(255,255,255,0.06);color:var(--txt);padding:6px 8px;border-radius:6px;}
  .small{font-size:12px;color:var(--muted);}
  footer{margin-top:12px;font-size:12px;color:var(--muted);}
</style>
</head>
<body>
<div id="wrap">
  <canvas id="c"></canvas>
  <div class="panel">
    <h1>3D Symmetrical Sculpture</h1>
    <div class="row"><label>Symmetry (folds)</label><input id="sym" type="range" min="2" max="12" step="1" value="6"><div class="small" id="symVal">6</div></div>
    <div class="row"><label>Rotation Speed</label><input id="speed" type="range" min="-1.5" max="1.5" step="0.01" value="0.18"><div class="small" id="speedVal">0.18</div></div>
    <div class="row"><label>Detail (scale)</label><input id="detail" type="range" min="0.6" max="2.5" step="0.01" value="1.25"><div class="small" id="detailVal">1.25</div></div>
    <div class="row"><label>Edge Sharpness</label><input id="sharp" type="range" min="0.2" max="2.5" step="0.01" value="1.0"><div class="small" id="sharpVal">1.00</div></div>
    <div class="row"><label>Glow / Bloom</label><input id="glow" type="range" min="0" max="2.5" step="0.01" value="0.9"><div class="small" id="glowVal">0.90</div></div>
    <div class="row"><label>Soft Shadows</label><input id="shad" type="range" min="0.0" max="1.0" step="0.01" value="0.8"><div class="small" id="shadVal">0.80</div></div>
    <div class="row"><label>AO Strength</label><input id="ao" type="range" min="0" max="2" step="0.01" value="1.0"><div class="small" id="aoVal">1.00</div></div>
    <div class="row"><label>Ray Steps</label><input id="steps" type="range" min="32" max="160" step="1" value="90"><div class="small" id="stepsVal">90</div></div>
    <div class="row"><label>Shade Color</label>
      <select id="pal">
        <option>Blue Crystal</option>
        <option>Emerald</option>
        <option>Violet Brass</option>
        <option>Gold Copper</option>
      </select>
    </div>

    <div style="display:flex;gap:8px;margin-top:8px;">
      <button id="pause">Pause</button>
      <button id="snap">Snapshot</button>
      <button id="reset">Reset View</button>
    </div>

    <footer>
      <div class="small">This is a ray-marched, symmetry-folded signed-distance sculpture with soft shading, AO and rim lights — use the sliders to sculpt it. If GPU is slow lower Ray Steps.</div>
    </footer>
  </div>
</div>

<script>
const canvas = document.getElementById('c');
const gl = canvas.getContext('webgl2',{antialias:true});
if(!gl){ alert('WebGL2 required'); throw new Error('WebGL2 required'); }

function setSize(){
  const dpr = Math.min(2, window.devicePixelRatio || 1);
  canvas.width = Math.floor(canvas.clientWidth * dpr);
  canvas.height = Math.floor(canvas.clientHeight * dpr);
  gl.viewport(0,0,canvas.width,canvas.height);
}
function fitCanvas(){
  // left pane width is 360px; canvas should fill remaining area
  const wrap = document.getElementById('wrap');
  const rect = wrap.getBoundingClientRect();
  canvas.style.height = (window.innerHeight - 24) + 'px';
  canvas.style.width = 'calc(100% - 384px)';
  setSize();
}
window.addEventListener('resize', fitCanvas);
fitCanvas();

// Helpers
function compileShader(src, type){
  const s = gl.createShader(type);
  gl.shaderSource(s, src);
  gl.compileShader(s);
  if(!gl.getShaderParameter(s, gl.COMPILE_STATUS)){
    console.error(gl.getShaderInfoLog(s));
    throw new Error('Shader compile error');
  }
  return s;
}
function linkProgram(vs,fs){
  const p = gl.createProgram();
  gl.attachShader(p, compileShader(vs, gl.VERTEX_SHADER));
  gl.attachShader(p, compileShader(fs, gl.FRAGMENT_SHADER));
  gl.linkProgram(p);
  if(!gl.getProgramParameter(p, gl.LINK_STATUS)){
    console.error(gl.getProgramInfoLog(p));
    throw new Error('Program link error');
  }
  return p;
}

// Vertex shader: full-screen quad
const vs = `#version 300 es
precision highp float;
layout(location=0) in vec2 a_pos;
out vec2 v_uv;
void main(){
  v_uv = a_pos * 0.5 + 0.5;
  gl_Position = vec4(a_pos,0.0,1.0);
}`;

// Fragment shader: ray marched symmetrical sculpture
const fs = `#version 300 es
precision highp float;
out vec4 outColor;
in vec2 v_uv;

uniform vec2 uRes;
uniform float uTime;
uniform int uSym;          // folds
uniform float uSpeed;
uniform float uDetail;     // scale of features
uniform float uSharp;      // edge sharpness
uniform float uGlow;       // glow strength
uniform float uShadow;     // soft shadow strength
uniform float uAO;         // AO
uniform int uSteps;        // max steps
uniform int uPal;          // palette

// camera
vec3 camPos = vec3(0.0, 0.0, 4.0);
vec3 lightDir = normalize(vec3(0.7, 0.6, 0.4));

// rotate a vector around Y
mat3 rotY(float a){
  float c=cos(a), s=sin(a);
  return mat3(c,0,s, 0,1,0, -s,0,c);
}
mat3 rotX(float a){
  float c=cos(a), s=sin(a);
  return mat3(1,0,0, 0,c,-s, 0,s,c);
}

// Folding symmetry: reflect angle into sector
vec3 foldSymmetry(vec3 p, int n){
  // rotate so symmetry around Z axis
  float angle = atan(p.y, p.x);
  float sector = 6.28318530718 / float(n);
  angle = mod(angle + 0.5*sector, sector) - 0.5*sector;
  float r = length(p.xy);
  p.x = cos(angle) * r;
  p.y = sin(angle) * r;
  return p;
}

// repeat / tile in spherical-ish way to add complexity
vec3 opRepeat(vec3 p){
  // spherical repetition
  float scale = 2.6;
  return mod(p*scale, 2.0) - 1.0;
}

// basic smooth min
float smin(float a,float b,float k){
  float h = max(k - abs(a-b), 0.0)/k;
  return min(a,b) - h*h*k*0.25;
}

// distance estimator for crystalline hybrid shape
float DE_raw(vec3 p){
  // base: rounded octa / spiky sphere mix
  float s = length(p) - 1.0;
  float oct = (abs(p.x) + abs(p.y) + abs(p.z) ) - 1.4;
  float blend = mix(s, oct, 0.5);

  // add radial ribs
  float r = length(p);
  float ribs = 0.05 * sin( (r*12.0 + uTime*0.8) * uDetail ) * exp(-r*2.4);

  // add planar creases
  float creases = 0.12 * (sin(p.x*6.0*uDetail + uTime*0.9) * sin(p.y*5.0*uDetail - uTime*0.7));

  float shape = blend - ribs - creases;

  // sharpen edges slightly
  shape = shape * uSharp;

  return shape;
}

// apply repeated folding & symmetry to make quasi-crystalline form
float DE(vec3 p){
  // apply symmetry folds iteratively to create kaleidoscopic sculpture
  vec3 q = p;
  // a few repeats to create nested facets
  for(int i=0;i<4;i++){
    q = foldSymmetry(q, uSym);
    q = opRepeat(q);
    // slight twist
    q = q * rotY(0.1*float(i) + uTime*0.05);
  }
  // scale detail inwards
  q *= 1.0 + 0.15 * sin(uTime*0.3);
  return DE_raw(q);
}

// numerical normal via gradient
vec3 calcNormal(vec3 p){
  float eps = 0.0008;
  float dx = DE(p + vec3(eps,0,0)) - DE(p - vec3(eps,0,0));
  float dy = DE(p + vec3(0,eps,0)) - DE(p - vec3(0,eps,0));
  float dz = DE(p + vec3(0,0,eps)) - DE(p - vec3(0,0,eps));
  return normalize(vec3(dx,dy,dz));
}

// soft shadow (raymarch toward light)
float softShadow(vec3 ro, vec3 rd){
  float res = 1.0;
  float t = 0.02;
  for(int i=0;i<40;i++){
    float h = DE(ro + rd * t);
    if(h < 0.0001) return 0.0;
    res = min(res, smoothstep(0.0,1.0, h / (t*uShadow*4.0) ));
    t += clamp(h, 0.02, 0.12);
    if(t > 8.0) break;
  }
  return clamp(res, 0.0, 1.0);
}

// ambient occlusion sampling
float ambientOcclusion(vec3 p, vec3 n){
  float ao = 0.0;
  float sca = 1.0;
  for(int i=0;i<6;i++){
    float hr = 0.02 + 0.06 * float(i);
    float d = DE(p + n * hr);
    ao += (hr - d) * sca;
    sca *= 0.6;
  }
  return clamp(1.0 - ao * uAO, 0.0, 1.0);
}

// main raymarch
float raymarch(vec3 ro, vec3 rd, out vec3 pOut){
  float t = 0.02;
  float dist;
  int i;
  for(i=0;i<uSteps;i++){
    vec3 p = ro + rd * t;
    dist = DE(p);
    if(dist < 0.0008) break;
    t += clamp(dist, 0.001, 0.18);
    if(t > 20.0) break;
  }
  pOut = ro + rd * t;
  if(t > 20.0) return -1.0;
  return t;
}

// palette selection
vec3 shadePalette(float v){
  if(uPal==0){
    return vec3(0.05,0.12,0.35) + v * vec3(0.4,0.7,1.0); // blue crystal
  } else if(uPal==1){
    return vec3(0.03,0.12,0.07) + v * vec3(0.5,1.0,0.6); // emerald
  } else if(uPal==2){
    return vec3(0.12,0.03,0.08) + v * vec3(1.0,0.6,1.0); // violet brass
  } else {
    return vec3(0.08,0.06,0.02) + v * vec3(1.0,0.8,0.45); // gold copper
  }
}

void main(){
  vec2 uv = (v_uv * 2.0 - 1.0) * vec2(uRes.x/uRes.y, 1.0);
  // camera setup with slight orbit
  vec3 ro = camPos * 1.0;
  ro *= 1.0 + 0.02*sin(uTime*0.4);
  // orbiting look at origin
  float ang = uTime * uSpeed;
  mat3 camRot = rotY(ang) * rotX(0.25 + 0.05*sin(uTime*0.2));
  ro = camRot * ro;
  vec3 target = vec3(0.0, 0.0, 0.0);
  vec3 fw = normalize(target - ro);
  vec3 right = normalize(cross(vec3(0.0,1.0,0.0), fw));
  vec3 up = normalize(cross(fw, right));
  // construct ray
  vec3 rd = normalize(uv.x*right + uv.y*up + 1.6*fw);

  // march
  vec3 pHit;
  float t = raymarch(ro, rd, pHit);
  vec3 col = vec3(0.0);

  if(t > 0.0){
    vec3 n = calcNormal(pHit);
    // lighting
    float diff = max(0.0, dot(n, lightDir));
    // specular (blinn)
    vec3 view = normalize(ro - pHit);
    vec3 halfv = normalize(lightDir + view);
    float spec = pow(max(0.0, dot(n, halfv)), 48.0);
    // rim
    float rim = pow(1.0 - max(0.0, dot(view,n)), 2.0);

    // shadows
    float sh = softShadow(pHit + n*0.001, lightDir);
    // AO
    float ao = ambientOcclusion(pHit, n);

    float v = diff * 0.9 + spec * 0.8 + rim * 0.6;
    v *= sh * ao;

    vec3 base = shadePalette(v);
    // apply fresnel-ish rim and glow
    float fres = pow(1.0 - max(0.0, dot(view, n)), 2.0);
    vec3 rimCol = vec3(1.0, 0.95, 0.9) * fres * 0.8;
    col = base * (0.6 + 0.8*diff) + rimCol * 0.6 + spec * vec3(1.0);
    // glow falloff by distance
    float distAttn = 1.0 / (1.0 + 0.18 * length(pHit - ro) * length(pHit - ro));
    col *= distAttn;
    // add subtle procedural veining based on world-space coordinates
    float veins = 0.15 * sin((pHit.x*12.0 + pHit.y*8.0 + pHit.z*10.0) * uDetail + uTime*0.8);
    col += veins * 0.25;
    // vignette
    float vig = smoothstep(1.4, 0.2, length(v_uv - 0.5) * 2.0);
    col *= mix(1.0, 0.85, vig);
    // additional glow layer using normal & fresnel
    col += rimCol * uGlow * 0.6;
  } else {
    // background gradient
    col = vec3(0.02,0.03,0.04) + 0.8 * vec3(0.02,0.04,0.07) * (1.0 - length(v_uv - 0.5) * 1.2);
  }

  // gamma
  col = pow(clamp(col, 0.0, 1.0), vec3(0.4545));
  outColor = vec4(col, 1.0);
}
`;

// compile & link
const prog = linkProgram(vs, fs);
gl.useProgram(prog);

// quad setup
const quad = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, quad);
gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
  -1,-1,  1,-1,  -1,1,  -1,1,  1,-1,  1,1
]), gl.STATIC_DRAW);
const vao = gl.createVertexArray();
gl.bindVertexArray(vao);
gl.enableVertexAttribArray(0);
gl.bindBuffer(gl.ARRAY_BUFFER, quad);
gl.vertexAttribPointer(0,2,gl.FLOAT,false,0,0);
gl.bindVertexArray(null);

// uniforms locations
const loc = {
  uRes: gl.getUniformLocation(prog, 'uRes'),
  uTime: gl.getUniformLocation(prog, 'uTime'),
  uSym: gl.getUniformLocation(prog, 'uSym'),
  uSpeed: gl.getUniformLocation(prog, 'uSpeed'),
  uDetail: gl.getUniformLocation(prog, 'uDetail'),
  uSharp: gl.getUniformLocation(prog, 'uSharp'),
  uGlow: gl.getUniformLocation(prog, 'uGlow'),
  uShadow: gl.getUniformLocation(prog, 'uShadow'),
  uAO: gl.getUniformLocation(prog, 'uAO'),
  uSteps: gl.getUniformLocation(prog, 'uSteps'),
  uPal: gl.getUniformLocation(prog, 'uPal')
};

// UI hooks
const els = {
  sym: document.getElementById('sym'),
  symVal: document.getElementById('symVal'),
  speed: document.getElementById('speed'),
  speedVal: document.getElementById('speedVal'),
  detail: document.getElementById('detail'),
  detailVal: document.getElementById('detailVal'),
  sharp: document.getElementById('sharp'),
  sharpVal: document.getElementById('sharpVal'),
  glow: document.getElementById('glow'),
  glowVal: document.getElementById('glowVal'),
  shad: document.getElementById('shad'),
  shadVal: document.getElementById('shadVal'),
  ao: document.getElementById('ao'),
  aoVal: document.getElementById('aoVal'),
  steps: document.getElementById('steps'),
  stepsVal: document.getElementById('stepsVal'),
  pal: document.getElementById('pal'),
  pause: document.getElementById('pause'),
  snap: document.getElementById('snap'),
  reset: document.getElementById('reset')
};

function updateUIlabels(){
  els.symVal.textContent = els.sym.value;
  els.speedVal.textContent = Number(els.speed.value).toFixed(2);
  els.detailVal.textContent = Number(els.detail.value).toFixed(2);
  els.sharpVal.textContent = Number(els.sharp.value).toFixed(2);
  els.glowVal.textContent = Number(els.glow.value).toFixed(2);
  els.shadVal.textContent = Number(els.shad.value).toFixed(2);
  els.aoVal.textContent = Number(els.ao.value).toFixed(2);
  els.stepsVal.textContent = els.steps.value;
}
[els.sym,els.speed,els.detail,els.sharp,els.glow,els.shad,els.ao,els.steps].forEach(el=>{
  el.addEventListener('input', updateUIlabels);
});
updateUIlabels();

// controls actions
let running = true;
els.pause.addEventListener('click', ()=>{ running = !running; els.pause.textContent = running ? 'Pause':'Resume'; });
els.reset.addEventListener('click', ()=>{ startT = performance.now(); });
els.snap.addEventListener('click', ()=>{ // snapshot PNG
  const url = canvas.toDataURL('image/png');
  const a = document.createElement('a');
  a.href = url; a.download = 'sculpture.png'; a.click();
});

// animation loop
let startT = performance.now();
function render(){
  if(!running){ requestAnimationFrame(render); return; }
  fitCanvas(); // ensure correct size (cheap)
  const now = performance.now();
  const t = (now - startT) * 0.001;
  gl.useProgram(prog);
  gl.bindVertexArray(vao);

  // uniforms
  gl.uniform2f(loc.uRes, canvas.width, canvas.height);
  gl.uniform1f(loc.uTime, t);
  gl.uniform1i(loc.uSym, parseInt(els.sym.value));
  gl.uniform1f(loc.uSpeed, Number(els.speed.value));
  gl.uniform1f(loc.uDetail, Number(els.detail.value));
  gl.uniform1f(loc.uSharp, Number(els.sharp.value));
  gl.uniform1f(loc.uGlow, Number(els.glow.value));
  gl.uniform1f(loc.uShadow, Number(els.shad.value));
  gl.uniform1f(loc.uAO, Number(els.ao.value));
  gl.uniform1i(loc.uSteps, parseInt(els.steps.value));
  gl.uniform1i(loc.uPal, els.pal.selectedIndex);

  gl.drawArrays(gl.TRIANGLES, 0, 6);
  requestAnimationFrame(render);
}
requestAnimationFrame(render);

</script>
</body>
</html>
